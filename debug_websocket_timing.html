<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Timing Debug</title>
    <style>
        body { font-family: monospace; padding: 20px; }
        .log { margin: 5px 0; padding: 5px; background: #f5f5f5; }
        .success { background: #d4edda; }
        .warning { background: #fff3cd; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <h1>🔍 WebSocket Timing Debug</h1>
    <p>This page helps debug the timing between global WebSocket and queue page loading.</p>
    
    <div id="logs"></div>
    
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            document.getElementById('logs').appendChild(div);
            console.log(message);
        }
        
        log('🚀 Debug page loaded');
        
        // Check initial state
        if (window.adminSocket) {
            log(`📊 Global WebSocket exists: readyState=${window.adminSocket.readyState}`, 'success');
            const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
            log(`📊 State: ${states[window.adminSocket.readyState] || 'UNKNOWN'}`, 'success');
        } else {
            log('❌ Global WebSocket not found', 'error');
        }
        
        if (window.liveUpdatesActive) {
            log('✅ Live updates active flag set', 'success');
        } else {
            log('❌ Live updates active flag not set', 'warning');
        }
        
        // Monitor for global WebSocket creation
        let checkCount = 0;
        const monitor = setInterval(() => {
            checkCount++;
            
            if (window.adminSocket) {
                const state = window.adminSocket.readyState;
                const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
                log(`📊 Check ${checkCount}: WebSocket ${states[state]}`, 
                    state === 1 ? 'success' : 'warning');
                
                if (state === 1) { // OPEN
                    log('🎉 WebSocket is ready for queue pages!', 'success');
                    clearInterval(monitor);
                }
            } else {
                log(`⏳ Check ${checkCount}: No WebSocket yet`, 'warning');
            }
            
            if (checkCount >= 30) {
                log('⏰ Stopped monitoring after 30 seconds', 'error');
                clearInterval(monitor);
            }
        }, 1000);
        
        // Test WebSocket creation timing
        setTimeout(() => {
            log('🧪 Testing manual WebSocket creation...');
            
            try {
                const testWs = new WebSocket('ws://localhost:8000/ws/admin/live/');
                
                testWs.onopen = function() {
                    log('✅ Manual WebSocket connected successfully', 'success');
                    testWs.close();
                };
                
                testWs.onerror = function(error) {
                    log('❌ Manual WebSocket error: ' + error, 'error');
                };
                
                testWs.onclose = function(event) {
                    log(`🔌 Manual WebSocket closed: code=${event.code}`, 'warning');
                };
                
            } catch (error) {
                log('❌ Failed to create manual WebSocket: ' + error, 'error');
            }
        }, 5000);
        
        // Instructions
        setTimeout(() => {
            log('📋 INSTRUCTIONS:', 'success');
            log('1. Open this page in one tab', 'success');
            log('2. Open admin queue page in another tab', 'success');
            log('3. Compare timing between WebSocket ready and queue page load', 'success');
            log('4. Check if queue page loads before WebSocket is ready', 'success');
        }, 1000);
    </script>
</body>
</html>
