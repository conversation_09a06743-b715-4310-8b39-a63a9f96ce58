#!/usr/bin/env python
"""
Debug the complete signal flow from database change to WebSocket
"""
import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def test_signal_flow():
    """Test the complete signal flow"""
    setup_django()
    
    print("🔍 DEBUGGING SIGNAL FLOW")
    print("=" * 60)
    
    try:
        from queue_system.models import QueuedJob
        from queue_system.signals import send_admin_live_update
        from django.db.models.signals import post_save
        from django.utils import timezone
        
        # Check if signals are connected
        print("📡 Checking signal connections...")
        
        # Get signal receivers for QueuedJob
        receivers = post_save._live_receivers(sender=QueuedJob)
        print(f"📊 Found {len(receivers)} post_save receivers for QueuedJob:")
        
        for receiver in receivers:
            print(f"  - {receiver}")
            
        if not receivers:
            print("❌ NO SIGNAL RECEIVERS FOUND!")
            print("💡 This means signals are not connected properly")
            return False
            
        # Test manual signal trigger
        print(f"\n🧪 Testing manual signal trigger...")
        send_admin_live_update()
        print("✅ Manual signal trigger completed")
        
        # Test database change signal
        print(f"\n🔄 Testing database change signal...")
        
        # Get a job to modify
        job = QueuedJob.objects.first()
        if not job:
            print("❌ No jobs found to test with")
            return False
            
        print(f"📝 Found job {job.id} with status: {job.status}")
        
        # Add signal debugging
        def debug_signal_handler(sender, instance, created, **kwargs):
            print(f"🚨 SIGNAL TRIGGERED! Job {instance.id}: {instance.status}")
            print(f"   Created: {created}")
            print(f"   Sender: {sender}")
            
        # Connect debug handler
        post_save.connect(debug_signal_handler, sender=QueuedJob)
        
        # Change job status
        original_status = job.status
        new_status = 'processing' if original_status != 'processing' else 'queued'
        
        print(f"🔄 Changing job {job.id}: {original_status} → {new_status}")
        job.status = new_status
        job.save()
        
        print(f"✅ Job saved. Signal should have been triggered.")
        
        # Change back
        job.status = original_status
        job.save()
        
        print(f"🔄 Changed back: {new_status} → {original_status}")
        
        # Disconnect debug handler
        post_save.disconnect(debug_signal_handler, sender=QueuedJob)
        
        return True
        
    except Exception as e:
        print(f"❌ Signal flow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_channel_layer():
    """Test if channel layer is working"""
    setup_django()
    
    print("\n🔍 TESTING CHANNEL LAYER")
    print("=" * 60)
    
    try:
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync
        
        channel_layer = get_channel_layer()
        
        if not channel_layer:
            print("❌ No channel layer configured!")
            return False
            
        print(f"✅ Channel layer found: {type(channel_layer).__name__}")
        print(f"📊 Channel layer config: {channel_layer}")
        
        # Test sending a message
        print(f"\n🧪 Testing channel layer message sending...")
        
        test_message = {
            "type": "admin_update",
            "data": {
                "type": "test",
                "message": "Test from debug script",
                "timestamp": "2025-01-01T00:00:00Z"
            }
        }
        
        async_to_sync(channel_layer.group_send)(
            "admin_updates",
            test_message
        )
        
        print("✅ Test message sent to admin_updates group")
        return True
        
    except Exception as e:
        print(f"❌ Channel layer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_consumer_connection():
    """Test if consumers are properly configured"""
    setup_django()
    
    print("\n🔍 TESTING CONSUMER CONFIGURATION")
    print("=" * 60)
    
    try:
        from queue_system.consumer import AdminLiveUpdatesConsumer
        from queue_system.routing import websocket_urlpatterns
        
        print("✅ AdminLiveUpdatesConsumer imported successfully")
        print(f"📊 WebSocket URL patterns: {len(websocket_urlpatterns)}")
        
        for pattern in websocket_urlpatterns:
            try:
                print(f"  - {pattern.pattern} → {pattern.callback}")
            except AttributeError:
                print(f"  - {pattern} → {getattr(pattern, 'callback', 'Unknown')}")
            
        # Check if admin live updates pattern exists
        admin_pattern = None
        for pattern in websocket_urlpatterns:
            if 'admin/live' in pattern.pattern.pattern:
                admin_pattern = pattern
                break
                
        if admin_pattern:
            print("✅ Admin live updates WebSocket pattern found")
        else:
            print("❌ Admin live updates WebSocket pattern NOT found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Consumer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_redis_connection():
    """Test Redis connection for channel layer"""
    print("\n🔍 TESTING REDIS CONNECTION")
    print("=" * 60)
    
    try:
        import redis
        
        # Test Redis connection
        r = redis.Redis(host='localhost', port=6379, db=0)
        ping_result = r.ping()
        
        if ping_result:
            print("✅ Redis connection successful")
            
            # Test Redis info
            info = r.info()
            print(f"📊 Redis version: {info.get('redis_version', 'Unknown')}")
            print(f"📊 Connected clients: {info.get('connected_clients', 'Unknown')}")
            
            return True
        else:
            print("❌ Redis ping failed")
            return False
            
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("💡 Make sure Redis is running: redis-server")
        return False

def main():
    print("🎯 COMPLETE SIGNAL FLOW DEBUGGING")
    print("=" * 70)
    
    print("📋 This will test the complete flow:")
    print("  1. Signal connections")
    print("  2. Database change triggers")
    print("  3. Channel layer functionality")
    print("  4. Consumer configuration")
    print("  5. Redis connection")
    
    # Run all tests
    tests = [
        ("Signal Flow", test_signal_flow),
        ("Channel Layer", test_channel_layer),
        ("Consumer Config", test_consumer_connection),
        ("Redis Connection", test_redis_connection),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*70}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 70)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("💡 The signal flow should be working correctly")
        print("🔍 If live updates still don't work, check:")
        print("  1. Browser WebSocket connection")
        print("  2. Django server console for signal messages")
        print("  3. Browser console for WebSocket messages")
    else:
        print(f"\n⚠️ SOME TESTS FAILED!")
        print("🔧 Fix the failing components before testing live updates")
        
    print(f"\n📋 Next steps:")
    print("1. Fix any failing tests above")
    print("2. Restart Django server")
    print("3. Open queue admin page")
    print("4. Run: python test_realtime_updates.py")
    print("5. Watch for live updates")

if __name__ == "__main__":
    main()
