from celery import shared_task
from django.db import transaction
from datetime import datetime, timedelta
from django.utils import timezone

import logging
import re
import json

# Import the configured Celery app to ensure correct broker settings
from config.celery import app as celery_app
from .models import QueuedJob, JobError
from orders.models import order as Order, BarbadosForm  # Import 'order' and alias it as 'Order'
import traceback

logger = logging.getLogger(__name__)

def parse_bot_error(error_message, error_trace):
    """Parse bot error messages to create detailed, user-friendly descriptions"""

    # Default values
    detailed_description = "Bot execution failed"
    failure_reason = "bot_execution_error"

    # Common bot error patterns and their descriptions
    error_patterns = {
        # Selenium WebDriver errors
        r"Error clicking element.*gender.*dropdown": {
            "description": "[SUCCESS] Bot successfully filled: Name, Surname, Arrival details, etc.\n[FAILED] Bot failed at: Gender dropdown selection\n[RETRY] Job automatically requeued for retry",
            "reason": "gender_dropdown_error"
        },
        r"Unable to select gender.*from the drop down menu": {
            "description": "[SUCCESS] <PERSON><PERSON> successfully filled: Name, Surname, Arrival details, etc.\n[FAILED] Bot failed at: Gender dropdown selection\n[RETRY] Job automatically requeued for retry",
            "reason": "gender_dropdown_error"
        },
        r"Error clicking element.*Message:.*Stacktrace:": {
            "description": "[SUCCESS] Bot successfully navigated to form\n[SUCCESS] Bot successfully filled initial fields\n[FAILED] Bot failed at: Element interaction (element not clickable)\n[RETRY] Job automatically requeued for retry",
            "reason": "element_interaction_error"
        },
        r"TimeoutException": {
            "description": "[SUCCESS] Bot successfully started\n[FAILED] Bot failed at: Page loading timeout\n[RETRY] Job automatically requeued for retry",
            "reason": "page_timeout_error"
        },
        r"NoSuchElementException": {
            "description": "[SUCCESS] Bot successfully navigated to form\n[FAILED] Bot failed at: Required form element not found\n[RETRY] Job automatically requeued for retry",
            "reason": "element_not_found_error"
        },
        r"WebDriverException": {
            "description": "[FAILED] Bot failed at: Browser/WebDriver initialization\n[RETRY] Job automatically requeued for retry",
            "reason": "webdriver_error"
        },
        r"ImportError.*run_barbados_bot": {
            "description": "[FAILED] Bot failed at: Bot module import\n[SYSTEM] System error: Bot file not accessible\n[RETRY] Job automatically requeued for retry",
            "reason": "bot_import_error"
        },
        r"Failed to import Barbados bot": {
            "description": "[FAILED] Bot failed at: Bot module import\n[SYSTEM] System error: Bot file not accessible\n[RETRY] Job automatically requeued for retry",
            "reason": "bot_import_error"
        }
    }

    # Check for specific error patterns
    combined_error = f"{error_message}\n{error_trace}"

    for pattern, info in error_patterns.items():
        if re.search(pattern, combined_error, re.IGNORECASE | re.DOTALL):
            detailed_description = info["description"]
            failure_reason = info["reason"]
            break

    # Extract additional context from the error trace
    if "Successfully" in error_trace and "Failed" in error_trace:
        # Parse successful steps from the trace
        successful_steps = re.findall(r'(Successfully [^\\n]+)', error_trace)
        failed_steps = re.findall(r'(Failed [^\\n]+|Error [^\\n]+)', error_trace)

        if successful_steps or failed_steps:
            steps_description = ""
            for step in successful_steps[:3]:  # Show first 3 successful steps
                steps_description += f"[SUCCESS] {step}\n"
            for step in failed_steps[:1]:  # Show first failed step
                steps_description += f"[FAILED] {step}\n"
            steps_description += "[RETRY] Job automatically requeued for retry"

            if steps_description:
                detailed_description = steps_description

    # Create a concise error message for logging
    if "gender" in error_message.lower():
        error_msg = "Gender dropdown selection failed"
    elif "timeout" in error_message.lower():
        error_msg = "Page loading timeout"
    elif "element" in error_message.lower():
        error_msg = "Element interaction failed"
    elif "import" in error_message.lower():
        error_msg = "Bot import failed"
    else:
        error_msg = "Bot execution failed"

    return error_msg, detailed_description, failure_reason

def determine_failure_reason(job, order_obj=None):
    """Determine the categorized failure reason based on job errors"""
    # Get the most recent error for this job
    last_error = JobError.objects.filter(job=job).order_by('-occurred_at').first()

    if not last_error:
        return 'unknown_error'

    error_message = last_error.error_message.lower()

    # Categorize based on error message patterns
    if ('no barbados form data found' in error_message or
        'no form data' in error_message or
        'form data not found' in error_message or
        'missing form data' in error_message):
        return 'missing_form_data'
    elif ('failed to import' in error_message or
          'import error' in error_message or
          'module not found' in error_message):
        return 'bot_configuration_error'
    elif 'no bot implementation found' in error_message:
        return 'bot_configuration_error'
    elif ('element not found' in error_message or
          'timeout' in error_message or
          'no such element' in error_message or
          'element is not clickable' in error_message):
        return 'website_structure_changed'
    elif ('login' in error_message or
          'authentication' in error_message or
          'unauthorized' in error_message or
          'access denied' in error_message):
        return 'authentication_failed'
    elif ('connection' in error_message or
          'network' in error_message or
          'refused' in error_message or
          'unreachable' in error_message):
        return 'network_error'
    elif ('invalid' in error_message and 'data' in error_message) or 'validation' in error_message:
        return 'invalid_order_data'
    elif ('missing' in error_message and ('field' in error_message or 'date' in error_message)):
        return 'missing_form_data'
    else:
        return 'bot_execution_error'

def categorize_error(exception):
    """Categorize an exception into a failure reason"""
    error_message = str(exception).lower()
    error_type = type(exception).__name__

    if 'timeout' in error_message:
        return 'website_structure_changed'
    elif 'element not found' in error_message or 'no such element' in error_message:
        return 'website_structure_changed'
    elif 'connection' in error_message or 'network' in error_message:
        return 'network_error'
    elif 'authentication' in error_message or 'login' in error_message:
        return 'authentication_failed'
    elif 'import' in error_message:
        return 'bot_configuration_error'
    elif error_type in ['ValueError', 'KeyError', 'AttributeError']:
        return 'invalid_order_data'
    else:
        return 'unknown_error'

@shared_task(bind=True, 
             serializer='json',
             name='queue_system.tasks.process_order',
             time_limit=300, soft_time_limit=240)
def process_order(self, order_id, location_id=None):
    # Validate input
    try:
        order_id = str(order_id)
        if location_id:
            location_id = str(location_id)
    except (TypeError, ValueError) as e:
        logger.error(f"Invalid task parameters: {e}")
        raise
    """Process an order with proper error handling and retries"""
    job = None
    try:
        with transaction.atomic():
            job = QueuedJob.objects.select_for_update().get(
                order_id=order_id,
                status__in=['queued', 'requeued']
            )

            job.status = 'processing'
            job.started_at = timezone.now()
            job.worker_id = self.request.id
            job.save()

        logger.info(f"Starting processing for job {job.id} (Order: {order_id})")

        order_obj = job.order
        location = job.location
        location_name = location.location_name
        base_location = location_name.split('-')[0] if '-' in location_name else location_name

        success = False

        if base_location.lower() == 'barbados':
            try:
                from external.bots.Barbados_form.Barbados_form_1 import run_barbados_bot
            except ImportError:
                logger.error("Failed to import Barbados bot")
                raise

            if BarbadosForm.objects.filter(order=order_obj).exists():
                form = BarbadosForm.objects.get(order=order_obj)
                try:
                    logger.info(f"Running Barbados bot for order {order_id}")
                    success = run_barbados_bot(form)
                except Exception as e:
                    # Parse detailed error information from bot execution
                    error_msg, detailed_description, failure_reason = parse_bot_error(str(e), traceback.format_exc())

                    logger.error(f"Barbados bot failed: {error_msg}")
                    logger.error(traceback.format_exc())

                    # Store detailed error information
                    error_trace = traceback.format_exc()
                    error_details = {
                        'error_type': type(e).__name__,
                        'error_message': str(e),
                        'detailed_description': detailed_description,
                        'order_id': str(order_id),
                        'location': location_name,
                        'timestamp': timezone.now().isoformat(),
                        'bot_module': 'Barbados_form_1',
                        'context': 'Bot execution failed during form processing'
                    }

                    JobError.objects.create(
                        job=job,
                        error_message=detailed_description,
                        error_trace=error_trace,
                        error_details=error_details
                    )

                    # Set specific failure reason
                    job.failure_reason = failure_reason
                    success = False
            else:
                error_msg = f"No Barbados form data found for order {order_id}"
                logger.error(error_msg)

                # Create detailed error with context
                error_details = {
                    'error_type': 'missing_form_data',
                    'order_id': str(order_id),
                    'location': location_name,
                    'timestamp': timezone.now().isoformat(),
                    'context': 'Barbados form data validation failed'
                }

                JobError.objects.create(
                    job=job,
                    error_message=error_msg,
                    error_trace=f"Missing form data validation:\n- Order ID: {order_id}\n- Location: {location_name}\n- Expected: BarbadosForm record\n- Found: None"
                )
        else:
            error_msg = f"No bot implementation found for location: {base_location}"
            logger.error(error_msg)
            JobError.objects.create(job=job, error_message=error_msg)

        if success:
            job.mark_as_completed()
            order_obj.status = 'bot_completed_form'
        else:
            # Handle job failure with retry logic
            job.retry_count += 1

            # Determine failure reason based on the error
            failure_reason = determine_failure_reason(job, order_obj)
            error_details = {
                'order_id': str(order_id),
                'location': location_name,
                'has_form_data': BarbadosForm.objects.filter(order=order_obj).exists() if base_location.lower() == 'barbados' else False,
                'timestamp': timezone.now().isoformat(),
                'worker_id': job.worker_id,
                'retry_count': job.retry_count
            }

            # Get the last error message from JobError
            last_error = JobError.objects.filter(job=job).order_by('-occurred_at').first()
            error_message = last_error.error_message if last_error else "Bot execution failed"

            # Preserve the original error message (don't overwrite with retry info)
            if not hasattr(job, '_original_error') or not job._original_error:
                # Store the first error as the original error
                job._original_error = error_message
                # Save original error in a field that won't be overwritten
                if not job.error_message or job.error_message.startswith('Retry '):
                    job.error_message = error_message

            # Check if we should retry or mark as failed
            if job.retry_count < job.max_retries:
                # Increment retry count for this attempt
                job.retry_count += 1

                # Reset job for retry
                job.status = 'queued'
                job.started_at = None
                job.worker_id = None
                # DON'T overwrite error_message with retry info - keep original error
                job.error_details = error_details
                job.failure_reason = failure_reason

                logger.info(f"Job {job.id} queued for retry {job.retry_count}/{job.max_retries}")

                # Try to requeue the job
                try:
                    queue_name = f'location.{job.location.id}'
                    # Use the configured Celery app to ensure correct broker settings
                    celery_app.send_task(
                        'queue_system.tasks.process_order',
                        args=[str(order_id), str(location_id) if location_id else str(job.location.id)],
                        queue=queue_name,
                        countdown=30  # Wait 30 seconds before retry
                    )
                    logger.info(f"Job {job.id} requeued for retry")
                except Exception as requeue_error:
                    logger.error(f"Failed to requeue job {job.id}: {str(requeue_error)}")
                    # Fall back to direct processing
                    from threading import Thread
                    def retry_in_background():
                        import time
                        time.sleep(30)  # Wait 30 seconds
                        process_order.delay(str(order_id), str(location_id) if location_id else None)

                    thread = Thread(target=retry_in_background)
                    thread.daemon = True
                    thread.start()
                    logger.info(f"Job {job.id} scheduled for background retry")

                order_obj.status = 'bot_submission_retry'
            else:
                # Max retries reached, mark as failed
                # Use the original error message, not "Max retries reached" wrapper
                original_error = getattr(job, '_original_error', error_message)
                job.mark_as_failed_final(
                    error_message=original_error,  # Keep the original bot error
                    error_details=error_details,
                    failure_reason=failure_reason
                )
                order_obj.status = 'bot_submission_failed'
                logger.error(f"Job {job.id} failed after {job.retry_count} retries")

        job.save()
        order_obj.save()

        return success

    except QueuedJob.DoesNotExist:
        logger.error(f"No queued job found for order {order_id}")
        return False
    except Exception as e:
        logger.exception(f"Unexpected error processing order {order_id}: {str(e)}")

        # Handle job retry logic manually
        if job:
            try:
                with transaction.atomic():
                    job.refresh_from_db()

                    # Determine if we should retry or fail
                    if job.retry_count < job.max_retries:
                        # Increment retry count for this attempt
                        job.retry_count += 1
                        # Retry the job
                        job.status = 'queued'  # Reset to queued for retry
                        job.started_at = None
                        job.worker_id = None

                        # Create error record
                        JobError.objects.create(
                            job=job,
                            error_message=f"Retry {job.retry_count}: {str(e)}",
                            error_trace=traceback.format_exc()
                        )

                        job.save()
                        logger.info(f"Job {job.id} queued for retry {job.retry_count}/{job.max_retries}")

                        # Requeue the job for processing
                        try:
                            queue_name = f'location.{job.location.id}'
                            # Use the configured Celery app to ensure correct broker settings
                            celery_app.send_task(
                                'queue_system.tasks.process_order',
                                args=[str(order_id), str(location_id) if location_id else str(job.location.id)],
                                queue=queue_name,
                                countdown=30  # Wait 30 seconds before retry
                            )
                            logger.info(f"Job {job.id} requeued for retry")
                        except Exception as requeue_error:
                            logger.error(f"Failed to requeue job {job.id}: {str(requeue_error)}")
                            # Fall back to direct processing
                            from threading import Thread
                            def retry_in_background():
                                import time
                                time.sleep(30)  # Wait 30 seconds
                                process_order.delay(str(order_id), str(location_id) if location_id else None)

                            thread = Thread(target=retry_in_background)
                            thread.daemon = True
                            thread.start()

                        return False
                    else:
                        # Max retries reached, mark as failed
                        failure_reason = categorize_error(e)
                        error_details = {
                            'error_type': type(e).__name__,
                            'error_message': str(e),
                            'order_id': str(order_id),
                            'timestamp': timezone.now().isoformat(),
                            'retry_count': job.retry_count,
                            'max_retries_reached': True
                        }

                        job.mark_as_failed_final(
                            error_message=f"Max retries reached: {str(e)}",
                            error_details=error_details,
                            failure_reason=failure_reason
                        )

                        logger.error(f"Job {job.id} failed after {job.retry_count} retries")
                        return False

            except Exception as retry_error:
                logger.error(f"Error handling retry for job {job.id if job else 'unknown'}: {str(retry_error)}")

        return False

@shared_task(
    bind=True,
    name='queue_system.tasks.check_waiting_queue',
    # base=Task,
    time_limit=300, soft_time_limit=240,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_kwargs={'max_retries': 3},
    queue='scheduler'  # Explicitly declare the queue here
)
def check_waiting_queue(self):
    """Safely check and enqueue waiting jobs"""
    try:
        logger.info(f"Starting check_waiting_queue (attempt {self.request.retries})")
        
        now = timezone.now()
        eligible_jobs = QueuedJob.objects.filter(
            status='queued',
            scheduled_for__lte=now
        ).select_related('order', 'location')

        logger.info(f"count check_waiting_queue  {eligible_jobs}")

        count = 0
        for job in eligible_jobs:
            try:
                with transaction.atomic():
                    job.status = 'queued'
                    job.save()
                    
                    # Explicit queue routing with safety checks
                    queue_name = f'location.{job.location.id}'
                    if not queue_name.startswith('location.'):
                        queue_name = 'default'
                        
                    try:
                        # Use the configured Celery app to ensure correct broker settings
                        result = celery_app.send_task(
                            'queue_system.tasks.process_order',
                            kwargs={'order_id': str(job.order.id), 'location_id': str(job.location.id)},
                            queue=queue_name,
                            serializer='json',
                            retry=True,
                            retry_policy={
                                'max_retries': 3,
                                'interval_start': 0,
                                'interval_step': 0.2,
                                'interval_max': 0.2,
                            }
                        )
                        logger.info(f"Task sent to queue {queue_name} with task ID: {result.id}")
                    except Exception as e:
                        logger.error(f"Failed to send task to queue {queue_name}: {str(e)}")
                        job.status = 'failed'
                        job.error_message = f"Failed to send to queue: {str(e)}"
                        job.save()

                    # from celery import current_app
                    # current_app.send_task(
                    #     'queue_system.tasks.process_order',
                    #     args=[str(job.order.id)],
                    #     kwargs={'location_id': str(job.location.id)},
                    #     queue=queue_name,
                    #     serializer='json'
                    # )

                    # process_order.apply_async(
                    #     args=[str(job.order.id)],
                    #     kwargs={'location_id': str(job.location.id)},
                    #     serializer='json'
                    #     # Let router handle queue assignment
                    # )
                    logger.info("check 11111")
                    count += 1
                    
            except Exception as job_error:
                logger.error(f"Failed to enqueue job {job.id}: {str(job_error)}")
                continue

        return {"status": "success", "enqueued": count}

    except Exception as e:
        logger.exception("Critical error in check_waiting_queue")
        raise self.retry(exc=e, countdown=min(60 * (self.request.retries + 1), 300))

@shared_task(name='queue_system.tasks.schedule_job', time_limit=300, soft_time_limit=240)
def schedule_job(order_id):
    """
    Create a queued job for an order and schedule it based on location config.
    """
    logger.info(f"schedule_job task started for order_id: {order_id}")
    try:
        # Get the order
        order_obj = Order.objects.get(id=order_id)
        logger.info(f"Found order: {order_obj.id} with status: {order_obj.status}")

        from queue_system.models import LocationQueueConfig
        
        # Get or create location config
        location_config, created = LocationQueueConfig.objects.get_or_create(
            location=order_obj.location,
            defaults={
                'has_time_window': False,
                'max_workers': 1,
                'active_workers': 0,
                'priority_level': 1
            }
        )
        logger.info(f"Using location config for {order_obj.location.location_name}, created: {created}")
        
        # Check if a job already exists for this order
        existing_job = QueuedJob.objects.filter(order=order_obj).first()
        if existing_job:
            logger.info(f"Job already exists for order {order_id}: {existing_job.id} with status: {existing_job.status}")
            
            # If job exists but is in failed status, requeue it
            if existing_job.status == 'failed':
                existing_job.status = 'queued'
                existing_job.retry_count += 1
                existing_job.save()
                
                # Schedule the job for processing
                queue_name = f'location.{order_obj.location.id}'
                logger.info(f"Requeuing failed job to queue: {queue_name}")
                
                try:
                    # Use the configured Celery app to ensure correct broker settings
                    result = celery_app.send_task(
                        'queue_system.tasks.process_order',
                        args=[str(order_id), str(order_obj.location.id)],
                        queue=queue_name,
                        retry=True,
                        retry_policy={
                            'max_retries': 3,
                            'interval_start': 0,
                            'interval_step': 0.2,
                            'interval_max': 0.2,
                        }
                    )
                    logger.info(f"Task sent to queue {queue_name} with task ID: {result.id}")
                except Exception as e:
                    logger.error(f"Failed to send task to queue {queue_name}: {str(e)}")
                
                logger.info(f"Job {existing_job.id} requeued for processing")
                
            return str(existing_job.id)
        
        # Create job record
        job = QueuedJob.objects.create(
            order=order_obj,
            location=order_obj.location,
            scheduled_for=timezone.now(),
            status='queued'  # Default to queued
        )
        
        logger.info(f"Created job {job.id} for order {order_id}")
        
        # Get the location ID
        location_id = order_obj.location.id
        
        # Schedule the job for processing by sending it to the location-specific queue
        queue_name = f'location.{location_id}'
        logger.info(f"Scheduling job for processing in queue: {queue_name}")
        
        # Robust job processing: Try Redis first, fall back to direct processing
        job_processed = False

        # Try Redis queue first
        try:
            # Use the configured Celery app to ensure correct broker settings
            result = celery_app.send_task(
                'queue_system.tasks.process_order',
                args=[str(order_id), str(location_id)],
                queue=queue_name,
                retry=True,
                retry_policy={
                    'max_retries': 3,
                    'interval_start': 0,
                    'interval_step': 0.2,
                    'interval_max': 0.2,
                }
            )
            logger.info(f"✓ Task sent to Redis queue {queue_name} with task ID: {result.id}")
            job_processed = True

        except Exception as e:
            logger.warning(f"Redis queue failed: {str(e)}")
            logger.info("→ Falling back to direct processing...")

            # Process the job directly as fallback
            try:
                from threading import Thread

                def process_in_background():
                    try:
                        logger.info(f"Starting direct processing for job {job.id}")
                        result = process_order.delay(str(order_id), str(job.order.location.id))
                        logger.info(f"Direct processing completed for job {job.id}, result: {result}")
                    except Exception as thread_e:
                        logger.error(f"Background processing failed for job {job.id}: {str(thread_e)}")

                thread = Thread(target=process_in_background, name=f"Job-{job.id}")
                thread.daemon = True
                thread.start()
                logger.info(f"✓ Job {job.id} started in background thread (PID: {thread.ident})")
                job_processed = True

            except Exception as e2:
                logger.error(f"Direct processing setup failed: {str(e2)}")
                job.status = 'failed'
                job.error_message = f"Both Redis queue and direct processing failed: {str(e2)}"
                job.save()

        if job_processed:
            logger.info(f"Job {job.id} successfully queued for processing")
        
        logger.info(f"Job {job.id} scheduled for processing in queue: {queue_name}")
        
        return str(job.id)
        
    except Order.DoesNotExist:
        logger.error(f"Order {order_id} not found")
        return None
    except Exception as e:
        logger.error(f"Error scheduling job for order {order_id}: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def get_travel_date(order_obj):
    """
    Extract travel date from order or related models.
    Implement based on your data model.
    """
    # This is a placeholder - implement based on your actual data model
    # For example, if you have a BarbadosForm with a travel_date field:
    try:
        from orders.models import BarbadosForm
        form = BarbadosForm.objects.get(order=order_obj)
        return form.travel_date
    except:
        # If no travel date found, default to 30 days from now
        return timezone.now() + timedelta(days=30)

@shared_task(name='queue_system.tasks.process_review_queue', time_limit=300, soft_time_limit=240)
def process_review_queue():
    """
    Automatically move failed jobs to review queue based on failure criteria
    """
    logger.info("Starting review queue processing")

    # Find failed jobs that should be moved to review
    failed_jobs = QueuedJob.objects.filter(status='failed')
    moved_count = 0

    for job in failed_jobs:
        if job.should_move_to_review():
            job.status = 'review'
            job.reviewed_at = timezone.now()
            job.save()
            moved_count += 1
            logger.info(f"Moved job {job.id} to review queue (reason: {job.failure_reason})")

    if moved_count > 0:
        logger.info(f"Moved {moved_count} jobs to review queue")

    return moved_count

@shared_task(name='queue_system.tasks.process_queued_jobs', time_limit=300, soft_time_limit=240)
def process_queued_jobs():
    """
    Check for queued jobs and process them directly.
    This is a fallback for when apply_async doesn't work with database broker.
    """
    logger.info("Starting process_queued_jobs task")

    # Get all queued jobs
    queued_jobs = QueuedJob.objects.filter(status='queued').order_by('created_at')

    if not queued_jobs.exists():
        logger.info("No queued jobs found")
        return "No queued jobs to process"

    count = 0
    for job in queued_jobs[:3]:  # Process max 3 jobs at a time to avoid overload
        logger.info(f"Processing queued job {job.id} for order {job.order.id}")

        try:
            # Process the job directly with proper parameters
            result = process_order.delay(order_id=str(job.order.id), location_id=str(job.order.location.id))
            if result:
                logger.info(f"Successfully queued job {job.id} for processing")
            else:
                logger.error(f"Failed to queue job {job.id}")
            count += 1
        except Exception as e:
            logger.error(f"Error processing job {job.id}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            count += 1

    logger.info(f"Processed {count} queued jobs")
    return f"Processed {count} queued jobs"

@shared_task(name='queue_system.tasks.process_failed_job', time_limit=300, soft_time_limit=240)
def process_failed_job(job_id):
    """
    Process a failed job by moving it to the error queue.
    """
    try:
        job = QueuedJob.objects.get(id=job_id)
        
        # Check if job is already in error status
        if job.status == 'failed':
            logger.info(f"Job {job_id} is already marked as failed")
            return False
        
        # Mark job as failed
        job.status = 'failed'
        job.save()
        
        # Update order status
        order_obj = job.order
        order_obj.status = 'bot_submission_failed'
        order_obj.save()
        
        logger.info(f"Moved job {job_id} to error queue")
        return True
    except QueuedJob.DoesNotExist:
        logger.error(f"Job {job_id} not found")
        return False
    except Exception as e:
        logger.exception(f"Error processing failed job {job_id}: {str(e)}")
        return False


@shared_task(name='queue_system.tasks.sync_worker_status_task', bind=False, time_limit=300, soft_time_limit=240)
def sync_worker_status_task():
    """Periodic task to sync database with actual Celery worker status (every 5 seconds)"""
    logger.info("Starting sync_worker_status_task")
    try:
        # Import here to avoid circular imports
        from queue_system.models import LocationQueueConfig
        import subprocess

        # Count actual Celery processes
        celery_count = 0
        try:
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq celery.exe'],
                                  capture_output=True, text=True, timeout=3, shell=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                celery_count = len([line for line in lines if 'celery.exe' in line and line.strip()])
        except:
            celery_count = 0

        # Calculate workers per location (subtract 2 for beat and scheduler)
        configs = LocationQueueConfig.objects.select_related('location').all()
        location_count = configs.count()

        if celery_count > 2 and location_count > 0:
            # Assume remaining workers are distributed among locations
            workers_per_location = max(0, (celery_count - 2) // location_count)
        else:
            workers_per_location = 0

        # Update database
        updated_count = 0
        for config in configs:
            current_active = config.active_workers

            # Update if different
            if current_active != workers_per_location:
                config.active_workers = workers_per_location
                config.save()
                updated_count += 1

        result = f"Synced {updated_count} locations, {celery_count} total processes, {workers_per_location} per location"
        logger.info(f"sync_worker_status_task completed: {result}")
        return result

    except Exception as e:
        logger.exception(f"Worker status sync failed: {e}")
        return f"Sync failed: {e}"










