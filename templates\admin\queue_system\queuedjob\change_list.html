{% extends "admin/change_list.html" %}
{% load admin_urls static admin_list %}

{% block extrahead %}
{{ block.super }}
<style>
    .queue-dashboard {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .queue-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    .stat-card {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .stat-label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
    }
    .queued { color: #ffc107; }
    .processing { color: #17a2b8; }
    .completed { color: #28a745; }
    .failed { color: #dc3545; }
    .review { color: #fd7e14; }
    .requeued { color: #6f42c1; }
    .priority { color: #e83e8c; }
    
    .failure-reasons {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        margin-top: 15px;
    }
    .failure-reasons h4 {
        margin-top: 0;
        color: #dc3545;
    }
    .failure-reason-item {
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
        border-bottom: 1px solid #eee;
    }
    .failure-reason-item:last-child {
        border-bottom: none;
    }
    
    .review-queue-highlight {
        background-color: #fff3cd !important;
        border-left: 4px solid #ffc107 !important;
    }
    .failed-job-highlight {
        background-color: #f8d7da !important;
        border-left: 4px solid #dc3545 !important;
    }

    .live-indicator {
        display: inline-flex;
        align-items: center;
        margin-left: 15px;
        font-size: 14px;
    }
    .live-indicator .dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
    }
    .live-indicator.connected .dot { background-color: #28a745; }
    .live-indicator.disconnected .dot { background-color: #dc3545; }
    .live-indicator.connecting .dot { background-color: #ffc107; }
    .stat-card.updated {
        animation: highlightUpdate 1s;
    }
    @keyframes highlightUpdate {
        0% { background-color: rgba(40, 167, 69, 0.1); }
        100% { background-color: white; }
    }
    .updated-row {
        animation: rowHighlight 1s;
    }
    @keyframes rowHighlight {
        0% { background-color: rgba(40, 167, 69, 0.2); }
        100% { background-color: inherit; }
    }

    .stat-card.updated {
        background-color: #d4edda !important;
        border-left: 4px solid #28a745 !important;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2) !important;
        transform: scale(1.02);
    }

    .stat-card {
        transition: all 0.3s ease;
    }

    .live-update-pulse {
        animation: pulse 1s ease-in-out;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

</style>
{% endblock %}

{% block content_title %}
<h1>Queue System Dashboard</h1>
{% endblock %}

{% block content %}
<div style="background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 8px; border: 1px solid #dee2e6;">
    <h3 style="margin-top: 0; color: #333;">🎛️ Queue Management Dashboards</h3>
    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
        <a href="{% url 'queue_system:queue_overview' %}" style="background: #007cba; color: white; padding: 12px 20px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
            📊 Queue Overview Dashboard
        </a>
        <a href="{% url 'queue_system:review_queue_dashboard' %}" style="background: #dc3545; color: white; padding: 12px 20px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
            🔍 Review Queue Dashboard
        </a>
        <a href="/admin/queue_system/queuedjob/" style="background: #28a745; color: white; padding: 12px 20px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
            📋 Standard Admin View
        </a>
    </div>
    <p style="margin-bottom: 0; margin-top: 10px; color: #666; font-size: 14px;">
        Use these dashboards for comprehensive queue monitoring and management.
    </p>
</div>

{{ block.super }}
{% endblock %}

{% block result_list %}
<div class="queue-dashboard">
    <h2>📊 Queue Statistics</h2>
    
    <div class="queue-stats">
        <div class="stat-card">
            <div class="stat-number queued">{{ queued_count }}</div>
            <div class="stat-label">⏳ Queued</div>
        </div>
        <div class="stat-card">
            <div class="stat-number processing">{{ processing_count }}</div>
            <div class="stat-label">🔄 Processing</div>
        </div>
        <div class="stat-card">
            <div class="stat-number completed">{{ completed_count }}</div>
            <div class="stat-label">✅ Completed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number failed">{{ failed_count }}</div>
            <div class="stat-label">❌ Failed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number review">{{ review_count }}</div>
            <div class="stat-label">🔍 Under Review</div>
        </div>
        <div class="stat-card">
            <div class="stat-number requeued">{{ requeued_count }}</div>
            <div class="stat-label">🔄 Requeued</div>
        </div>
        <div class="stat-card">
            <div class="stat-number priority">{{ priority_count }}</div>
            <div class="stat-label">⚡ Priority</div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live indicator in dashboard header
    const dashboardHeader = document.querySelector('.queue-dashboard h2');
    if (dashboardHeader) {
        const indicator = document.createElement('span');
        indicator.className = 'live-indicator connecting';
        indicator.innerHTML = '<span class="dot"></span><span>Connecting...</span>';
        dashboardHeader.appendChild(indicator);
    }

    // Use global live updates with fallback to show current data
    console.log('📋 Queue page loaded - setting up live updates');

    // Always show current data first (from server-side rendering)
    updateConnectionStatus('connected', 'Live Updates');
    console.log('✅ Queue statistics showing current data');

    // Function to set up message listener for queue updates
    function setupQueueMessageListener() {
        if (!window.adminSocket) {
            console.log('❌ No global WebSocket available for queue updates');
            return false;
        }

        console.log('🔧 Setting up queue message listener on global WebSocket');

        // Store original handler to avoid overwriting
        if (!window.adminSocket.originalOnMessage) {
            window.adminSocket.originalOnMessage = window.adminSocket.onmessage;
            console.log('💾 Stored original WebSocket message handler');
        }

        // Set up combined message handler
        window.adminSocket.onmessage = function(event) {
            console.log('📨 Global WebSocket received message:', event.data.substring(0, 100) + '...');

            // Call original handler first (for global stats widget)
            if (window.adminSocket.originalOnMessage) {
                window.adminSocket.originalOnMessage(event);
            }

            // Handle queue page updates
            try {
                const data = JSON.parse(event.data);
                console.log('📊 Queue page processing WebSocket data:', data.type, data);

                if (data.type === 'initial_data' || data.type === 'admin_update') {
                    console.log('✅ Updating queue dashboard with:', data.data);
                    updateDashboard(data.data);
                } else {
                    console.log('⚠️ Unrecognized message type:', data.type);
                }
            } catch (error) {
                console.error('❌ Error parsing global WebSocket message:', error, event.data);
            }
        };

        console.log('✅ Queue message listener set up successfully');
        return true;
    }

    // Check if global WebSocket is available and connected
    if (window.adminSocket && window.adminSocket.readyState === WebSocket.OPEN) {
        console.log('✅ Global WebSocket already connected - setting up queue updates');
        console.log('🔍 WebSocket state:', window.adminSocket.readyState);
        console.log('🔍 WebSocket URL:', window.adminSocket.url);
        updateConnectionStatus('connected', 'Live Updates');
        setupQueueMessageListener();

        // Test if we can trigger an immediate update
        console.log('🧪 Testing immediate data request...');
        if (window.adminSocket.send) {
            try {
                window.adminSocket.send(JSON.stringify({type: 'request_update'}));
                console.log('📤 Sent test request to WebSocket');
            } catch (e) {
                console.log('❌ Failed to send test request:', e);
            }
        }
    } else if (window.adminSocket && window.adminSocket.readyState === WebSocket.CONNECTING) {
        console.log('🔄 Global WebSocket connecting - waiting for connection');
        updateConnectionStatus('connecting', 'Connecting...');

        // Wait for connection to complete
        const originalOnOpen = window.adminSocket.onopen;
        window.adminSocket.onopen = function(event) {
            if (originalOnOpen) originalOnOpen(event);
            console.log('✅ Global WebSocket connected - setting up queue updates');
            updateConnectionStatus('connected', 'Live Updates');
            setupQueueMessageListener();
        };
    } else {
        console.log('⚠️ Global WebSocket not available yet - will connect when ready');
        // Keep showing "Live Updates" since we have current data
        // The global WebSocket will enhance with real-time updates when ready

        // Check periodically for global WebSocket (less aggressively)
        let checkCount = 0;
        const maxChecks = 30; // Check for 30 seconds

        const checkGlobalSocket = setInterval(() => {
            checkCount++;

            if (window.adminSocket && window.adminSocket.readyState === WebSocket.OPEN) {
                console.log('✅ Global WebSocket now available - enhancing with real-time updates');
                setupQueueMessageListener();
                clearInterval(checkGlobalSocket);
            } else if (checkCount >= maxChecks) {
                console.log('⏳ Global WebSocket not available - starting polling fallback');
                clearInterval(checkGlobalSocket);
                startPollingFallback();
            }
        }, 2000); // Check every 2 seconds instead of every second
    }

    return;
    
    ws.onopen = function() {
        console.log('WebSocket connected');
        updateConnectionStatus('connected', 'Live Updates');
    };
    
    ws.onmessage = function(event) {
        const message = JSON.parse(event.data);
        console.log('WebSocket message received:', message);

        // Handle the admin live updates format
        if (message.type === 'initial_data' || message.type === 'admin_update') {
            updateDashboard(message.data);
        } else {
            // Handle direct data format (fallback)
            updateDashboard(message);
        }
    };
    
    ws.onclose = function() {
        updateConnectionStatus('disconnected', 'Offline - Reconnecting...');
        setTimeout(() => {
            window.location.reload();
        }, 5000);
    };
    
    ws.onerror = function(error) {
        console.error('WebSocket error:', error);
        updateConnectionStatus('disconnected', 'Connection Error');
    };
    
    function updateConnectionStatus(status, text) {
        const indicator = document.querySelector('.live-indicator');
        if (indicator) {
            indicator.className = 'live-indicator ' + status;
            const textSpan = indicator.querySelector('span:last-child');
            if (textSpan) textSpan.textContent = text;
        }
    }
    
    function updateDashboard(data) {
        console.log('🎯 updateDashboard called with data:', data);
        console.log('🔍 Data structure:', JSON.stringify(data, null, 2));

        // Handle the admin live updates data structure
        const overview = data.overview || data;
        console.log('📊 Overview data:', overview);

        // Update summary stats - map from overview structure to template variables
        updateStatCard('queued', overview.queued || data.queued_count || 0);
        updateStatCard('processing', overview.processing || data.processing_count || 0);
        updateStatCard('completed', overview.completed || data.completed_count || 0);
        updateStatCard('failed', overview.failed || data.failed_count || 0);
        updateStatCard('review', overview.review || data.review_count || 0);
        updateStatCard('requeued', data.requeued_count || 0);
        updateStatCard('priority', data.priority_count || 0);
        
        // Update failure reasons
        if (data.failure_reasons) {
            const container = document.querySelector('.failure-reasons');
            if (container) {
                container.innerHTML = `
                    <h4>🚨 Top Failure Reasons</h4>
                    ${data.failure_reasons.map(reason => `
                        <div class="failure-reason-item">
                            <span>${reason.failure_reason || 'Unknown'}</span>
                            <span class="badge">${reason.count}</span>
                        </div>
                    `).join('')}
                `;
            }
        }
        
        // Update job table rows
        if (data.recent_jobs && Array.isArray(data.recent_jobs)) {
            console.log(`🔄 Updating ${data.recent_jobs.length} job rows`);

            data.recent_jobs.forEach(job => {
                const row = document.querySelector(`tr[data-object-id="${job.id}"]`);
                if (row) {
                    console.log(`📝 Updating job row ${job.id}: status=${job.status}`);

                    // Update status cell
                    const statusCell = row.querySelector('td:nth-child(4)'); // Assuming status is 4th column
                    if (statusCell && job.status) {
                        const statusIcon = getStatusIcon(job.status);
                        const statusText = job.status.charAt(0).toUpperCase() + job.status.slice(1);
                        statusCell.innerHTML = `${statusIcon} ${statusText}`;
                    }

                    // Update retries cell
                    const retriesCell = row.querySelector('td:nth-child(5)'); // Assuming retries is 5th column
                    if (retriesCell && job.retries !== undefined) {
                        retriesCell.textContent = `${job.retries}/3`;
                    }

                    // Update failure reason cell
                    const failureCell = row.querySelector('td:nth-child(6)'); // Assuming failure reason is 6th column
                    if (failureCell && job.failure_reason) {
                        failureCell.textContent = job.failure_reason;
                    }

                    // Add visual feedback
                    row.classList.add('updated-row');
                    row.style.backgroundColor = '#fff3cd';
                    setTimeout(() => {
                        row.classList.remove('updated-row');
                        row.style.backgroundColor = '';
                    }, 2000);

                    console.log(`✅ Updated job row ${job.id}`);
                }
            });
        }

        // Highlight updated rows (legacy support)
        if (data.updated_jobs) {
            data.updated_jobs.forEach(jobId => {
                const row = document.querySelector(`tr[data-object-id="${jobId}"]`);
                if (row) {
                    row.classList.add('updated-row');
                    setTimeout(() => row.classList.remove('updated-row'), 1000);
                }
            });
        }
    }

    function startPollingFallback() {
        console.log('🔄 Starting polling fallback for live updates');

        // Poll for updates every 3 seconds
        const pollInterval = setInterval(async () => {
            try {
                console.log('📡 Polling for queue updates...');

                const response = await fetch('/queue/api/queue-updates/', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json',
                    },
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    const result = await response.json();

                    if (result.success && result.has_update) {
                        console.log('📊 Received polling update:', result.data);

                        // Process the update using the same logic as WebSocket
                        updateDashboard(result);
                        console.log('✅ Dashboard updated via polling');
                    } else if (result.success && !result.has_update) {
                        console.log('⚪ No updates available');
                    } else {
                        console.log('⚠️ Polling request failed:', result.error);
                    }
                } else {
                    console.log('⚠️ Polling request failed:', response.status);
                }

            } catch (error) {
                console.log('❌ Polling error:', error);
            }
        }, 3000); // Poll every 3 seconds

        // Store interval ID for cleanup
        window.queuePollingInterval = pollInterval;

        console.log('✅ Polling fallback started (3s interval)');
    }



    function getStatusIcon(status) {
        const icons = {
            'queued': '⏳',
            'processing': '🔄',
            'completed': '✅',
            'failed': '❌',
            'review': '🔍',
            'cancelled': '🚫'
        };
        return icons[status] || '❓';
    }

    function updateStatCard(type, value) {
        console.log(`🔄 updateStatCard: ${type} = ${value}`);

        // Try multiple selectors to find the stat card
        let card = document.querySelector(`.stat-number.${type}`);
        if (!card) {
            // Try alternative selectors based on the HTML structure
            card = document.querySelector(`[data-stat="${type}"] .stat-number`);
        }
        if (!card) {
            // Try finding by the stat card content
            const statCards = document.querySelectorAll('.stat-card');
            for (let statCard of statCards) {
                const label = statCard.querySelector('.stat-label');
                if (label && label.textContent.toLowerCase().includes(type.toLowerCase())) {
                    card = statCard.querySelector('.stat-number');
                    break;
                }
            }
        }

        if (!card) {
            console.log(`❌ No card found for type: ${type}`);
            console.log(`🔍 Available stat cards:`, document.querySelectorAll('.stat-card'));
            return;
        }

        const currentValue = card.textContent.trim();
        const newValue = value.toString();
        console.log(`📊 ${type}: "${currentValue}" → "${newValue}"`);

        if (currentValue !== newValue) {
            // Update the value
            card.textContent = newValue;

            // Add visual feedback
            const statCard = card.closest('.stat-card');
            if (statCard) {
                statCard.classList.add('updated');
                statCard.style.transform = 'scale(1.05)';
                statCard.style.transition = 'transform 0.3s ease';

                setTimeout(() => {
                    statCard.classList.remove('updated');
                    statCard.style.transform = 'scale(1)';
                }, 1000);
            }

            console.log(`✅ Updated ${type} card: "${currentValue}" → "${newValue}"`);
        } else {
            console.log(`⚪ ${type} unchanged (${newValue})`);
        }
    }
    
    // Your existing row highlighting code
    const rows = document.querySelectorAll('#result_list tbody tr');
    rows.forEach(row => {
        const statusCell = row.querySelector('td:nth-child(4)');
        if (statusCell) {
            const statusText = statusCell.textContent.trim();
            if (statusText.includes('🔍') || statusText.includes('Under Review')) {
                row.classList.add('review-queue-highlight');
            } else if (statusText.includes('❌') || statusText.includes('Failed')) {
                row.classList.add('failed-job-highlight');
            }
        }
    });
});
</script>

{{ block.super }}
{% endblock %}
