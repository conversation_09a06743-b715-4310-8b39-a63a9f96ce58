#!/usr/bin/env python
"""
Test WebSocket connection to admin live updates
"""
import asyncio
import websockets
import json
import sys

async def test_websocket_connection():
    """Test WebSocket connection to admin live updates"""
    
    print("🔍 TESTING WEBSOCKET CONNECTION")
    print("=" * 50)
    
    uri = "ws://localhost:8000/ws/admin/live/"
    
    try:
        print(f"📡 Connecting to: {uri}")
        
        # Connect to WebSocket
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected successfully!")
            
            # Wait for initial data
            print("⏳ Waiting for initial data...")
            
            try:
                # Wait for a message with timeout
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(message)
                
                print("📨 Received initial data:")
                print(f"   Type: {data.get('type', 'Unknown')}")
                
                if 'data' in data and 'overview' in data['data']:
                    overview = data['data']['overview']
                    print(f"   Total Jobs: {overview.get('total_jobs', 'N/A')}")
                    print(f"   Queued: {overview.get('queued', 'N/A')}")
                    print(f"   Processing: {overview.get('processing', 'N/A')}")
                    print(f"   Completed: {overview.get('completed', 'N/A')}")
                    print(f"   Failed: {overview.get('failed', 'N/A')}")
                    print(f"   Review: {overview.get('review', 'N/A')}")
                
                print("✅ WebSocket communication working!")
                return True
                
            except asyncio.TimeoutError:
                print("⏰ Timeout waiting for initial data")
                print("💡 WebSocket connected but no data received")
                return False
                
    except ConnectionRefusedError:
        print("❌ Connection refused - Django server not running?")
        print("💡 Start Django server: python manage.py runserver 8000")
        return False

    except websockets.InvalidStatusCode as e:
        print(f"❌ Invalid status code: {e}")
        print("💡 Check WebSocket routing and consumer configuration")
        return False
        
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

async def main():
    print("🎯 WEBSOCKET CONNECTION TEST")
    print("=" * 60)
    
    print("📋 This will test:")
    print("  1. WebSocket connection to admin live updates")
    print("  2. Initial data reception")
    print("  3. Consumer functionality")
    
    print(f"\n🔧 Prerequisites:")
    print("  1. Django server running on localhost:8000")
    print("  2. Admin user logged in (for authentication)")
    print("  3. WebSocket consumer properly configured")
    
    success = await test_websocket_connection()
    
    print(f"\n📊 TEST RESULT")
    print("=" * 60)
    
    if success:
        print("🎉 WebSocket connection test PASSED!")
        print("\n✅ What this means:")
        print("  - WebSocket consumer is working")
        print("  - Routing is configured correctly")
        print("  - Initial data is being sent")
        print("  - Ready for live updates")
        
        print(f"\n🚀 Next steps:")
        print("  1. Open queue admin page in browser")
        print("  2. Open browser console (F12)")
        print("  3. Look for WebSocket connection messages")
        print("  4. Test live updates with database changes")
        
    else:
        print("❌ WebSocket connection test FAILED!")
        print("\n🔧 Troubleshooting:")
        print("  1. Make sure Django server is running")
        print("  2. Check for consumer errors in Django console")
        print("  3. Verify routing configuration")
        print("  4. Check authentication requirements")
        
    print(f"\n💡 Manual test:")
    print("  Open: http://localhost:8000/admin/queue_system/queuedjob/")
    print("  Check browser console for WebSocket messages")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
