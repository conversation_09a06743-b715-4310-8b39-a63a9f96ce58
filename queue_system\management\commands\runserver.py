"""
Custom runserver command that forces ASGI mode for WebSocket support
"""
from django.core.management.commands.runserver import Command as BaseRunserverCommand
from django.conf import settings
import os
import sys

class Command(BaseRunserverCommand):
    help = 'Starts a lightweight Web server for development with ASGI support.'

    def add_arguments(self, parser):
        super().add_arguments(parser)
        parser.add_argument(
            '--force-asgi',
            action='store_true',
            help='Force ASGI mode for WebSocket support',
        )

    def handle(self, *args, **options):
        # Check if ASGI is configured
        if hasattr(settings, 'ASGI_APPLICATION') and settings.ASGI_APPLICATION:
            self.stdout.write(
                self.style.SUCCESS('🚀 Starting Django with ASGI support for WebSockets')
            )
            self.stdout.write(f'ASGI Application: {settings.ASGI_APPLICATION}')
            
            # Check if channels is available
            try:
                import channels
                self.stdout.write(f'✅ Channels version: {channels.__version__}')
            except ImportError:
                self.stdout.write(
                    self.style.WARNING('⚠️ Channels not installed - WebSockets may not work')
                )
            
            # Check channel layers
            if hasattr(settings, 'CHANNEL_LAYERS'):
                backend = settings.CHANNEL_LAYERS.get('default', {}).get('BACKEND', 'None')
                self.stdout.write(f'✅ Channel Layer: {backend}')
            
            self.stdout.write('🌐 WebSocket endpoint: ws://localhost:8000/ws/admin/live/')
            self.stdout.write('=' * 60)
        
        # Call the parent runserver command
        super().handle(*args, **options)

    def get_handler(self, *args, **options):
        """
        Return the ASGI application if configured, otherwise fall back to WSGI
        """
        if hasattr(settings, 'ASGI_APPLICATION') and settings.ASGI_APPLICATION:
            try:
                from django.core.asgi import get_asgi_application
                from django.utils.module_loading import import_string
                
                # Import the ASGI application
                application = import_string(settings.ASGI_APPLICATION)
                
                self.stdout.write(
                    self.style.SUCCESS('✅ Using ASGI application for WebSocket support')
                )
                
                return application
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ Failed to load ASGI application: {e}')
                )
                self.stdout.write('🔄 Falling back to WSGI mode')
        
        # Fall back to default WSGI handler
        return super().get_handler(*args, **options)
