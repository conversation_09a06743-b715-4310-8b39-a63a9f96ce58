#!/usr/bin/env python
"""
Test the WebSocket message type fix
"""
import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def test_message_type_fix():
    """Test the WebSocket message type fix"""
    print("🔧 WEBSOCKET MESSAGE TYPE FIX")
    print("=" * 50)
    
    print("✅ Fix Applied:")
    print("  • Changed signal message type from 'admin.update' to 'admin_update'")
    print("  • Now matches consumer method name exactly")
    print("  • WebSocket messages should now be processed correctly")
    
    print("\n🎯 Expected Flow:")
    print("  1. Job created/updated/deleted")
    print("  2. Signal sends 'admin_update' message")
    print("  3. Consumer receives message in admin_update() method")
    print("  4. Consumer sends data to WebSocket clients")
    print("  5. Queue page receives and processes update")
    print("  6. Statistics update in real-time")

def test_signal_trigger():
    """Test triggering a signal to send updates"""
    setup_django()
    
    print("\n🧪 TESTING SIGNAL TRIGGER")
    print("=" * 50)
    
    try:
        from queue_system.models import QueuedJob
        from django.utils import timezone
        
        # Get current job count
        initial_count = QueuedJob.objects.count()
        print(f"📊 Initial job count: {initial_count}")
        
        if initial_count > 0:
            # Update an existing job to trigger signal
            job = QueuedJob.objects.first()
            old_status = job.status
            
            # Change status to trigger update
            new_status = 'processing' if old_status != 'processing' else 'queued'
            job.status = new_status
            job.save()
            
            print(f"✅ Updated job {job.id}: {old_status} → {new_status}")
            print("📡 This should trigger admin_update signal")
            
            # Change it back
            job.status = old_status
            job.save()
            print(f"🔄 Reverted job {job.id}: {new_status} → {old_status}")
            print("📡 This should trigger another admin_update signal")
            
            return True
        else:
            print("⚠️ No jobs found to update")
            print("💡 Create some test jobs first")
            return False
            
    except Exception as e:
        print(f"❌ Signal test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_debugging_guide():
    """Show debugging guide for testing"""
    print("\n🔍 DEBUGGING GUIDE")
    print("=" * 50)
    
    print("📋 How to test the fix:")
    print("  1. Restart Django server (to apply signal changes)")
    print("  2. Open queue admin page")
    print("  3. Open browser console (F12)")
    print("  4. Run: python test_websocket_message_fix.py")
    print("  5. Watch for these console messages:")
    
    print("\n✅ SUCCESS Pattern (Browser Console):")
    print("  📨 Global WebSocket received message: ...")
    print("  📊 Queue page processing WebSocket data: admin_update")
    print("  🎯 updateDashboard called with data: ...")
    print("  🔄 updateStatCard: queued = X")
    print("  ✅ Updated queued card to X")
    
    print("\n✅ SUCCESS Pattern (Django Console):")
    print("  📊 Initial data sent to username")
    print("  [Signal triggered]")
    print("  📡 Sending admin update via WebSocket")
    print("  [No errors]")
    
    print("\n❌ FAILURE Patterns:")
    print("  • No 'admin_update' messages in browser console")
    print("  • 'updateDashboard' not called")
    print("  • Numbers don't change in UI")
    print("  • WebSocket errors in Django console")

def main():
    print("🎯 WEBSOCKET MESSAGE TYPE FIX - VERIFICATION")
    print("=" * 60)
    
    test_message_type_fix()
    
    # Test signal trigger
    signal_ok = test_signal_trigger()
    
    show_debugging_guide()
    
    print("\n📊 SUMMARY")
    print("=" * 50)
    print("✅ Message type fix applied: 'admin.update' → 'admin_update'")
    print(f"{'✅' if signal_ok else '❌'} Signal trigger test: {'PASS' if signal_ok else 'FAIL'}")
    
    print("\n🚀 NEXT STEPS")
    print("=" * 50)
    print("1. 🔄 Restart Django server:")
    print("   python manage.py runserver 8000")
    print()
    print("2. 🌐 Open queue admin page:")
    print("   http://localhost:8000/admin/queue_system/queuedjob/")
    print()
    print("3. 🧪 Test live updates:")
    print("   python test_websocket_message_fix.py")
    print()
    print("4. 👀 Watch browser console for debug messages")
    print("5. 📊 Verify queue statistics update in real-time")
    
    if signal_ok:
        print("\n🎉 Ready to test! The message type fix should resolve the live update issue.")
    else:
        print("\n⚠️ Signal test had issues, but the fix should still work.")
        print("   Try creating some test jobs first.")

if __name__ == "__main__":
    main()
