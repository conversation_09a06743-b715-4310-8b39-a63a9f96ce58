{% extends "admin/base.html" %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name"><a href="{% url 'admin:index' %}">{{ site_header|default:_('Django administration') }}</a></h1>
{% endblock %}

{% block nav-global %}{% endblock %}

{% block extrahead %}
{{ block.super }}
<!-- Live Updates Integration - Inline for reliability -->
<script>
// Live Updates WebSocket Integration for Django Admin
// Global WebSocket connection - prevent multiple instances
if (!window.adminSocket) {
    window.adminSocket = null;
}
if (!window.reconnectAttempts) {
    window.reconnectAttempts = 0;
}

let adminSocket = window.adminSocket;
let reconnectAttempts = window.reconnectAttempts;
const MAX_RECONNECT_ATTEMPTS = 3;  // Reduced to prevent excessive reconnection
const RECONNECT_DELAY = 5000;  // Increased delay to prevent rapid reconnection

document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname.includes('/admin/')) {
        initializeLiveUpdates();
    }
});

function initializeLiveUpdates() {
    // Prevent multiple initializations
    if (window.liveUpdatesInitialized) {
        console.log('⚠️ Live Updates already initialized, skipping...');
        return;
    }

    console.log('🚀 Initializing Live Updates...');
    window.liveUpdatesInitialized = true;

    createLiveIndicator();
    createStatsWidget();
    connectAdminWebSocket();

    window.addEventListener('beforeunload', function() {
        if (adminSocket && adminSocket.readyState === WebSocket.OPEN) {
            adminSocket.close();
        }
    });
}

function createLiveIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'live-indicator';
    indicator.innerHTML = '<span class="dot"></span><span>Connecting...</span>';
    indicator.style.cssText = `
        position: fixed; top: 10px; right: 20px; z-index: 9999;
        padding: 8px 15px; border-radius: 20px; font-size: 12px;
        font-weight: bold; color: white; cursor: pointer;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        transition: all 0.3s ease;
    `;

    const style = document.createElement('style');
    style.textContent = `
        #live-indicator .dot {
            display: inline-block; width: 8px; height: 8px;
            border-radius: 50%; background: white; margin-right: 8px;
            animation: blink 1s infinite;
        }
        @keyframes blink { 0%, 50% { opacity: 1; } 51%, 100% { opacity: 0.3; } }
        .live-indicator-connected {
            background: linear-gradient(45deg, #28a745, #20c997) !important;
            animation: pulse 2s infinite;
        }
        .live-indicator-disconnected {
            background: linear-gradient(45deg, #dc3545, #e83e8c) !important;
        }
        @keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.05); } 100% { transform: scale(1); } }
    `;
    document.head.appendChild(style);

    indicator.addEventListener('click', toggleStatsWidget);
    document.body.appendChild(indicator);
}

function createStatsWidget() {
    const widget = document.createElement('div');
    widget.id = 'admin-stats-widget';
    widget.style.cssText = `
        position: fixed; bottom: 20px; right: 20px; background: white;
        border: 1px solid #ddd; border-radius: 8px; padding: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1); min-width: 250px;
        z-index: 9998; font-size: 13px; max-height: 300px;
        overflow-y: auto; display: none;
    `;

    widget.innerHTML = `
        <button onclick="toggleStatsWidget()" style="position: absolute; top: 5px; right: 5px; background: none; border: none; font-size: 16px; cursor: pointer; color: #666;">×</button>
        <h4 style="margin: 0 0 10px 0; color: #333; border-bottom: 1px solid #eee; padding-bottom: 5px;">📊 Live Queue Stats</h4>
        <div id="stats-content">
            <div style="display: flex; justify-content: space-between; margin: 5px 0; padding: 3px 0;">
                <span style="color: #666;">Total Jobs:</span>
                <span style="font-weight: bold; color: #333;" id="total-jobs">-</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 5px 0; padding: 3px 0;">
                <span style="color: #666;">⏳ Queued:</span>
                <span style="font-weight: bold; color: #333;" id="queued-jobs">-</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 5px 0; padding: 3px 0;">
                <span style="color: #666;">🔄 Processing:</span>
                <span style="font-weight: bold; color: #333;" id="processing-jobs">-</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 5px 0; padding: 3px 0;">
                <span style="color: #666;">✅ Completed:</span>
                <span style="font-weight: bold; color: #333;" id="completed-jobs">-</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 5px 0; padding: 3px 0;">
                <span style="color: #666;">❌ Failed:</span>
                <span style="font-weight: bold; color: #333;" id="failed-jobs">-</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 5px 0; padding: 3px 0;">
                <span style="color: #666;">🔍 Review:</span>
                <span style="font-weight: bold; color: #333;" id="review-jobs">-</span>
            </div>
        </div>
        <div style="margin-top: 15px; border-top: 1px solid #eee; padding-top: 10px;">
            <h4 style="margin: 0 0 10px 0; color: #333;">📋 Recent Jobs</h4>
            <div id="recent-jobs-list" style="color: #999; font-style: italic;">Loading...</div>
        </div>
    `;

    document.body.appendChild(widget);
}

function connectAdminWebSocket() {
    // Prevent multiple connections
    if (adminSocket && (adminSocket.readyState === WebSocket.CONNECTING || adminSocket.readyState === WebSocket.OPEN)) {
        console.log('⚠️ WebSocket already connecting/connected, skipping...');
        return;
    }

    const wsScheme = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
    const wsPath = wsScheme + window.location.host + '/ws/admin/live/';

    console.log('🔗 Connecting to WebSocket:', wsPath);
    adminSocket = new WebSocket(wsPath);
    window.adminSocket = adminSocket;  // Store globally

    adminSocket.onopen = function(e) {
        console.log('✅ Admin WebSocket connected');
        reconnectAttempts = 0;
        window.reconnectAttempts = 0;  // Reset global counter
        updateConnectionStatus('connected', '🟢 Live Updates');
        showStatsWidget();
    };

    adminSocket.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            console.log('📨 Received update:', data.type);
            if (data.type === 'initial_data' || data.type === 'admin_update') {
                updateAdminData(data.data);
            }
        } catch (error) {
            console.error('❌ Error parsing WebSocket message:', error);
        }
    };

    adminSocket.onclose = function(event) {
        console.log('🔌 Admin WebSocket closed:', event.code, event.reason);
        updateConnectionStatus('disconnected', '🔴 Offline');
        hideStatsWidget();

        // Only reconnect if it wasn't a manual close (code 1000) and we haven't exceeded attempts
        if (event.code !== 1000 && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            reconnectAttempts++;
            window.reconnectAttempts = reconnectAttempts;  // Store globally
            console.log(`🔄 Reconnection attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}`);
            updateConnectionStatus('connecting', '🟡 Reconnecting...');
            setTimeout(connectAdminWebSocket, RECONNECT_DELAY);
        } else if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
            console.log('❌ Max reconnection attempts reached');
            updateConnectionStatus('disconnected', '🔴 Connection Failed');
        }
    };

    adminSocket.onerror = function(error) {
        console.error('❌ Admin WebSocket error:', error);
        updateConnectionStatus('disconnected', '⚠️ Error');
    };
}

function updateConnectionStatus(status, text) {
    const indicator = document.getElementById('live-indicator');
    if (indicator) {
        indicator.className = 'live-indicator-' + status;
        const textSpan = indicator.querySelector('span:last-child');
        if (textSpan) textSpan.textContent = text;
    }
}

function updateAdminData(data) {
    if (data.overview) {
        const elements = {
            'total-jobs': data.overview.total_jobs || 0,
            'queued-jobs': data.overview.queued || 0,
            'processing-jobs': data.overview.processing || 0,
            'completed-jobs': data.overview.completed || 0,
            'failed-jobs': data.overview.failed || 0,
            'review-jobs': data.overview.review || 0
        };

        for (const [id, value] of Object.entries(elements)) {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        }
    }

    if (data.recent_jobs) {
        updateRecentJobs(data.recent_jobs);
    }
}

function updateRecentJobs(jobs) {
    const container = document.getElementById('recent-jobs-list');
    if (!container) return;

    if (jobs.length === 0) {
        container.innerHTML = '<div style="color: #999; font-style: italic;">No recent jobs</div>';
        return;
    }

    container.innerHTML = jobs.slice(0, 5).map(job => `
        <div style="padding: 5px 0; border-bottom: 1px solid #f5f5f5; font-size: 11px;">
            <span style="display: inline-block; padding: 2px 6px; border-radius: 3px; color: white; font-size: 10px; margin-right: 5px; background: ${getStatusColor(job.status)};">${job.status}</span>
            <strong>${job.order__first_name} ${job.order__surname}</strong>
            <br>
            <small>${job.location__location_name} • ${job.retry_count}/${job.max_retries} retries</small>
        </div>
    `).join('');
}

function getStatusColor(status) {
    const colors = {
        'queued': '#6c757d', 'processing': '#007bff', 'completed': '#28a745',
        'failed': '#dc3545', 'review': '#ffc107'
    };
    return colors[status] || '#6c757d';
}

function showStatsWidget() {
    const widget = document.getElementById('admin-stats-widget');
    if (widget) widget.style.display = 'block';
}

function hideStatsWidget() {
    const widget = document.getElementById('admin-stats-widget');
    if (widget) widget.style.display = 'none';
}

function toggleStatsWidget() {
    const widget = document.getElementById('admin-stats-widget');
    if (widget) {
        widget.style.display = widget.style.display === 'none' ? 'block' : 'none';
    }
}

window.toggleStatsWidget = toggleStatsWidget;
</script>
{% endblock %}
