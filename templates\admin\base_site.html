{% extends "admin/base.html" %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name"><a href="{% url 'admin:index' %}">{{ site_header|default:_('Django administration') }}</a></h1>
{% endblock %}

{% block nav-global %}{% endblock %}

{% block extrahead %}
{{ block.super }}
<!-- Live Updates Integration - Clean Single Instance -->
<script>
// Prevent multiple WebSocket instances - CLEAN SLATE APPROACH
if (window.liveUpdatesActive) {
    console.log('⚠️ Live updates already active, skipping initialization');
} else {
    window.liveUpdatesActive = true;

    // Single global WebSocket connection
    let adminSocket = null;
    let reconnectAttempts = 0;
    let isConnecting = false;
    const MAX_RECONNECT_ATTEMPTS = 2;
    const RECONNECT_DELAY = 10000; // 10 seconds to prevent rapid loops

    document.addEventListener('DOMContentLoaded', function() {
        if (window.location.pathname.includes('/admin/')) {
            console.log('🚀 Starting Live Updates (Clean Version)');
            createLiveIndicator();
            createStatsWidget();

            // Wait a bit before connecting to ensure page is fully loaded
            setTimeout(function() {
                connectWebSocket();
            }, 1000);
        }
    });

    function connectWebSocket() {
        // Prevent multiple connections
        if (isConnecting || (adminSocket && adminSocket.readyState !== WebSocket.CLOSED)) {
            console.log('⚠️ WebSocket already connecting or connected, skipping...');
            return;
        }

        isConnecting = true;
        const wsUrl = (window.location.protocol === 'https:' ? 'wss://' : 'ws://') +
                      window.location.host + '/ws/admin/live/';

        console.log('🔗 Connecting to:', wsUrl);
        updateIndicator('connecting', '🟡 Connecting...');

        try {
            adminSocket = new WebSocket(wsUrl);

            adminSocket.onopen = function() {
                console.log('✅ WebSocket connected successfully');
                isConnecting = false;
                reconnectAttempts = 0;
                updateIndicator('connected', '🟢 Live Updates');
                showStatsWidget();
            };

            adminSocket.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    console.log('📨 Received:', message.type);
                    if (message.data && message.data.overview) {
                        updateStats(message.data.overview);
                    }
                } catch (e) {
                    console.error('❌ Message parse error:', e);
                }
            };

            adminSocket.onclose = function(event) {
                console.log('🔌 WebSocket closed:', event.code, event.reason);
                isConnecting = false;
                updateIndicator('disconnected', '🔴 Offline');
                hideStatsWidget();

                // Only reconnect if not a normal close and haven't exceeded attempts
                if (event.code !== 1000 && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                    reconnectAttempts++;
                    console.log(`🔄 Will reconnect in ${RECONNECT_DELAY/1000}s (attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
                    updateIndicator('connecting', '🟡 Reconnecting...');
                    setTimeout(connectWebSocket, RECONNECT_DELAY);
                } else {
                    console.log('❌ Not reconnecting (code:', event.code, 'attempts:', reconnectAttempts, ')');
                }
            };

            adminSocket.onerror = function(error) {
                console.error('❌ WebSocket error:', error);
                isConnecting = false;
                updateIndicator('disconnected', '⚠️ Error');
            };

        } catch (error) {
            console.error('❌ Failed to create WebSocket:', error);
            isConnecting = false;
            updateIndicator('disconnected', '🔴 Failed');
        }
    }

    function createLiveIndicator() {
        if (document.getElementById('live-indicator')) return; // Prevent duplicates

        const indicator = document.createElement('div');
        indicator.id = 'live-indicator';
        indicator.innerHTML = '<span class="dot"></span><span>Starting...</span>';
        indicator.style.cssText = `
            position: fixed; top: 10px; right: 20px; z-index: 9999;
            padding: 8px 15px; border-radius: 20px; font-size: 12px;
            font-weight: bold; color: white; cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            background: #6c757d; transition: all 0.3s ease;
        `;

        const style = document.createElement('style');
        style.textContent = `
            #live-indicator .dot {
                display: inline-block; width: 8px; height: 8px;
                border-radius: 50%; background: white; margin-right: 8px;
            }
        `;
        document.head.appendChild(style);

        indicator.addEventListener('click', toggleStatsWidget);
        document.body.appendChild(indicator);
    }

    function updateIndicator(status, text) {
        const indicator = document.getElementById('live-indicator');
        if (!indicator) return;

        const colors = {
            'connecting': '#ffc107',
            'connected': '#28a745',
            'disconnected': '#dc3545'
        };

        indicator.style.background = colors[status] || '#6c757d';
        const textSpan = indicator.querySelector('span:last-child');
        if (textSpan) textSpan.textContent = text;
    }

    function createStatsWidget() {
        if (document.getElementById('admin-stats-widget')) return; // Prevent duplicates

        const widget = document.createElement('div');
        widget.id = 'admin-stats-widget';
        widget.style.cssText = `
            position: fixed; bottom: 20px; right: 20px; background: white;
            border: 1px solid #ddd; border-radius: 8px; padding: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); min-width: 250px;
            z-index: 9998; font-size: 13px; display: none;
        `;

        widget.innerHTML = `
            <button onclick="toggleStatsWidget()" style="position: absolute; top: 5px; right: 5px; background: none; border: none; font-size: 16px; cursor: pointer; color: #666;">×</button>
            <h4 style="margin: 0 0 10px 0; color: #333;">📊 Live Queue Stats</h4>
            <div id="stats-content">
                <div>Total: <span id="total-jobs">-</span></div>
                <div>Queued: <span id="queued-jobs">-</span></div>
                <div>Processing: <span id="processing-jobs">-</span></div>
                <div>Completed: <span id="completed-jobs">-</span></div>
                <div>Failed: <span id="failed-jobs">-</span></div>
                <div>Review: <span id="review-jobs">-</span></div>
            </div>
        `;

        document.body.appendChild(widget);
    }

    function updateStats(overview) {
        const elements = {
            'total-jobs': overview.total_jobs || 0,
            'queued-jobs': overview.queued || 0,
            'processing-jobs': overview.processing || 0,
            'completed-jobs': overview.completed || 0,
            'failed-jobs': overview.failed || 0,
            'review-jobs': overview.review || 0
        };

        for (const [id, value] of Object.entries(elements)) {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        }
    }

    function showStatsWidget() {
        const widget = document.getElementById('admin-stats-widget');
        if (widget) widget.style.display = 'block';
    }

    function hideStatsWidget() {
        const widget = document.getElementById('admin-stats-widget');
        if (widget) widget.style.display = 'none';
    }

    function toggleStatsWidget() {
        const widget = document.getElementById('admin-stats-widget');
        if (widget) {
            widget.style.display = widget.style.display === 'none' ? 'block' : 'none';
        }
    }

    // Make toggle function globally available
    window.toggleStatsWidget = toggleStatsWidget;

} // End of liveUpdatesActive check






</script>
{% endblock %}
