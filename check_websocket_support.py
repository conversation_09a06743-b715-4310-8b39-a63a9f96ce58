#!/usr/bin/env python
"""
Check if Django server supports WebSockets
"""
import os
import sys
import django
import requests
import websocket
import json
import threading
import time

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def check_django_server():
    """Check if Django server is running"""
    try:
        response = requests.get('http://localhost:8000/admin/', timeout=5)
        print(f"✅ Django server is running (Status: {response.status_code})")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ Django server not accessible: {e}")
        return False

def check_websocket_endpoint():
    """Check if WebSocket endpoint is accessible"""
    try:
        ws = websocket.create_connection("ws://localhost:8000/ws/admin/live/", timeout=5)
        print("✅ WebSocket endpoint is accessible")
        
        # Try to receive initial data
        try:
            result = ws.recv()
            data = json.loads(result)
            print(f"✅ Received initial data: {data.get('type', 'unknown')}")
        except Exception as e:
            print(f"⚠️ No initial data received: {e}")
        
        ws.close()
        return True
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")
        return False

def check_redis_connection():
    """Check Redis connection"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        result = r.ping()
        print(f"✅ Redis connection successful: {result}")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

def check_channels_configuration():
    """Check Django Channels configuration"""
    setup_django()
    
    try:
        from django.conf import settings
        asgi_app = getattr(settings, 'ASGI_APPLICATION', None)
        channel_layers = getattr(settings, 'CHANNEL_LAYERS', None)
        
        print(f"✅ ASGI Application: {asgi_app}")
        print(f"✅ Channel Layers: {channel_layers}")
        
        # Test channel layer
        from channels.layers import get_channel_layer
        channel_layer = get_channel_layer()
        if channel_layer:
            print(f"✅ Channel layer active: {type(channel_layer).__name__}")
        else:
            print("❌ Channel layer not configured")
            
        return True
    except Exception as e:
        print(f"❌ Channels configuration error: {e}")
        return False

def test_websocket_with_auth():
    """Test WebSocket with authentication"""
    print("\n🔐 Testing WebSocket with authentication...")
    
    # First get a session cookie
    session = requests.Session()
    try:
        # Get login page to get CSRF token
        login_page = session.get('http://localhost:8000/admin/login/')
        
        # For now, just test without auth
        print("⚠️ Authentication test skipped - testing anonymous connection")
        
        # Test WebSocket connection
        ws = websocket.create_connection("ws://localhost:8000/ws/admin/live/", timeout=5)
        print("✅ WebSocket connected (may be rejected due to auth)")
        
        # Wait for potential auth rejection
        time.sleep(1)
        
        try:
            result = ws.recv()
            print(f"✅ Received: {result}")
        except:
            print("⚠️ Connection may have been closed due to authentication")
        
        ws.close()
        
    except Exception as e:
        print(f"❌ WebSocket auth test failed: {e}")

def main():
    print("🔍 DJANGO WEBSOCKET DIAGNOSTICS")
    print("=" * 50)
    
    # Check components
    checks = [
        ("Redis Connection", check_redis_connection),
        ("Django Channels Config", check_channels_configuration),
        ("Django Server", check_django_server),
        ("WebSocket Endpoint", check_websocket_endpoint),
    ]
    
    results = {}
    for name, check_func in checks:
        print(f"\n🔍 Checking {name}...")
        results[name] = check_func()
    
    # Additional auth test
    if results.get("WebSocket Endpoint"):
        test_websocket_with_auth()
    
    # Summary
    print("\n📊 SUMMARY")
    print("=" * 50)
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{name}: {status}")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS")
    print("=" * 50)
    
    if not results.get("Redis Connection"):
        print("• Start Redis server: redis-server")
    
    if not results.get("Django Server"):
        print("• Start Django server: python manage.py runserver")
    
    if not results.get("WebSocket Endpoint"):
        print("• Django may not be running with ASGI support")
        print("• Try: python run_django_server.py")
        print("• Or install: pip install daphne")
    
    if all(results.values()):
        print("🎉 All checks passed! WebSocket live updates should work.")
        print("📋 Next steps:")
        print("1. Open admin interface: http://localhost:8000/admin/")
        print("2. Look for live indicator in top-right corner")
        print("3. Test updates: python manage.py test_live_updates")

if __name__ == "__main__":
    main()
