#!/usr/bin/env python
"""
Test job processing to identify why queued jobs are not starting
"""
import os
import sys
import django
import time

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def test_job_processing():
    """Test job processing step by step"""
    setup_django()
    
    print("🔍 TESTING JOB PROCESSING")
    print("=" * 60)
    
    try:
        from queue_system.models import QueuedJob, JobError
        from queue_system.tasks import process_order
        from orders.models import BarbadosForm
        
        # Get the queued job
        job = QueuedJob.objects.filter(status='queued').first()
        if not job:
            print("❌ No queued jobs found")
            return False
            
        print(f"📝 Found Job {job.id}:")
        print(f"   Order: {job.order.first_name} {job.order.surname}")
        print(f"   Location: {job.location.location_name}")
        print(f"   Status: {job.status}")
        
        # Check if BarbadosForm exists
        barbados_form = BarbadosForm.objects.filter(order=job.order).first()
        if not barbados_form:
            print("❌ No BarbadosForm found for this order")
            return False
            
        print(f"✅ BarbadosForm found: {barbados_form.id}")
        
        print(f"\n🔄 TESTING BOT IMPORT")
        print("-" * 40)
        
        # Test bot import (with timeout)
        try:
            print("📦 Attempting to import Barbados bot...")
            
            # Set a timeout for the import
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError("Import timeout")
            
            # Set timeout (Windows doesn't support SIGALRM, so we'll use a different approach)
            try:
                from external.bots.Barbados_form.Barbados_form_1 import run_barbados_bot
                print("✅ Successfully imported run_barbados_bot")
                
                # Test if the function is callable
                if callable(run_barbados_bot):
                    print("✅ run_barbados_bot is callable")
                else:
                    print("❌ run_barbados_bot is not callable")
                    return False
                    
            except ImportError as e:
                print(f"❌ Import failed: {e}")
                return False
            except Exception as e:
                print(f"❌ Import error: {e}")
                import traceback
                traceback.print_exc()
                return False
                
        except Exception as e:
            print(f"❌ Bot import test failed: {e}")
            return False
        
        print(f"\n🔄 TESTING MANUAL JOB PROCESSING")
        print("-" * 40)
        
        # Test manual job processing
        try:
            print(f"🚀 Calling process_order manually...")
            print(f"   Order ID: {job.order.id}")
            print(f"   Location ID: {job.location.id}")
            
            # Call the task function directly (not via Celery)
            result = process_order(str(job.order.id), str(job.location.id))
            
            print(f"✅ Manual processing completed")
            print(f"📊 Result: {result}")
            
            # Check job status after processing
            job.refresh_from_db()
            print(f"📝 Job status after processing: {job.status}")
            
            if job.status == 'completed':
                print("🎉 Job completed successfully!")
                return True
            elif job.status == 'failed':
                print(f"❌ Job failed: {job.error_message}")
                
                # Check for detailed errors
                errors = JobError.objects.filter(job=job)
                for error in errors:
                    print(f"❌ Error details: {error.error_message}")
                    print(f"❌ Error trace: {error.error_trace[:500]}...")
                return False
            else:
                print(f"⚠️ Job in unexpected status: {job.status}")
                return False
                
        except Exception as e:
            print(f"❌ Manual processing failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🎯 JOB PROCESSING TEST")
    print("=" * 70)
    
    print("📋 This test will:")
    print("  1. Find a queued job")
    print("  2. Test bot import")
    print("  3. Run job processing manually")
    print("  4. Check results")
    
    print(f"\n⚠️ IMPORTANT:")
    print("  - This will actually process a real job")
    print("  - Make sure you have Chrome/ChromeDriver available")
    print("  - The bot will attempt to fill out the actual form")
    
    response = input("\n⏰ Continue with the test? (y/N): ")
    if response.lower() != 'y':
        print("❌ Test cancelled")
        return
    
    success = test_job_processing()
    
    if success:
        print(f"\n🎉 TEST SUCCESSFUL!")
        print(f"📋 The job processing system is working correctly")
        print(f"📋 The issue might be with Celery worker configuration")
    else:
        print(f"\n❌ TEST FAILED!")
        print(f"📋 There's an issue with the job processing system")
        print(f"📋 Check the error messages above for details")
        
    print(f"\n🔧 Next steps:")
    print(f"  1. If test successful: Check Celery worker logs")
    print(f"  2. If test failed: Fix the bot import/execution issues")
    print(f"  3. Check Chrome/ChromeDriver installation")
    print(f"  4. Verify bot dependencies are installed")

if __name__ == "__main__":
    main()
