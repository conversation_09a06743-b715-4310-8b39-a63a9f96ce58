<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Debug Tool</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { background: white; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .log { margin: 5px 0; padding: 8px; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .status { font-weight: bold; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 WebSocket Debug Tool</h1>
        <p>This tool helps debug WebSocket connections and message flow for the queue system.</p>
        
        <div class="section">
            <h2>📊 Connection Status</h2>
            <div id="status" class="status disconnected">🔴 Not Connected</div>
            <button onclick="connectWebSocket()">🔌 Connect WebSocket</button>
            <button onclick="disconnectWebSocket()">🔌 Disconnect WebSocket</button>
            <button onclick="testMessage()">📤 Send Test Message</button>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
        
        <div class="section">
            <h2>📨 Message Log</h2>
            <div id="messages"></div>
        </div>
        
        <div class="section">
            <h2>🧪 Test Actions</h2>
            <button onclick="triggerUpdate()">🚀 Trigger Manual Update</button>
            <button onclick="checkGlobalSocket()">🔍 Check Global Socket</button>
            <button onclick="testQueueUpdate()">📊 Test Queue Update</button>
        </div>
        
        <div class="section">
            <h2>📋 Instructions</h2>
            <ol>
                <li><strong>Connect WebSocket:</strong> Click "Connect WebSocket" to establish connection</li>
                <li><strong>Check Messages:</strong> Watch the message log for incoming data</li>
                <li><strong>Trigger Updates:</strong> Use test actions to send updates</li>
                <li><strong>Compare with Queue Page:</strong> Open queue admin page in another tab</li>
            </ol>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;
        
        function log(message, type = 'info') {
            messageCount++;
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `<strong>${messageCount}.</strong> ${new Date().toLocaleTimeString()}: ${message}`;
            document.getElementById('messages').appendChild(div);
            document.getElementById('messages').scrollTop = document.getElementById('messages').scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = message;
        }
        
        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket already connected', 'warning');
                return;
            }
            
            log('🔌 Connecting to WebSocket...', 'info');
            updateStatus('connecting', '🟡 Connecting...');
            
            try {
                ws = new WebSocket('ws://localhost:8000/ws/admin/live/');
                
                ws.onopen = function(event) {
                    log('✅ WebSocket connected successfully!', 'success');
                    updateStatus('connected', '🟢 Connected');
                };
                
                ws.onmessage = function(event) {
                    log(`📨 Received message: ${event.data.substring(0, 200)}...`, 'success');
                    
                    try {
                        const data = JSON.parse(event.data);
                        log(`📊 Message type: ${data.type}`, 'info');
                        
                        if (data.data && data.data.overview) {
                            const overview = data.data.overview;
                            log(`📈 Queue data - Queued: ${overview.queued}, Processing: ${overview.processing}, Completed: ${overview.completed}, Failed: ${overview.failed}, Review: ${overview.review}`, 'success');
                        }
                        
                        // Show full data structure
                        const pre = document.createElement('pre');
                        pre.textContent = JSON.stringify(data, null, 2);
                        const logDiv = document.createElement('div');
                        logDiv.className = 'log info';
                        logDiv.innerHTML = `<strong>Full Message Data:</strong>`;
                        logDiv.appendChild(pre);
                        document.getElementById('messages').appendChild(logDiv);
                        
                    } catch (e) {
                        log(`❌ Failed to parse message: ${e}`, 'error');
                    }
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`, 'error');
                    updateStatus('disconnected', '🔴 Error');
                };
                
                ws.onclose = function(event) {
                    log(`🔌 WebSocket closed: code=${event.code}, reason=${event.reason}`, 'warning');
                    updateStatus('disconnected', '🔴 Disconnected');
                };
                
            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error}`, 'error');
                updateStatus('disconnected', '🔴 Failed');
            }
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                log('🔌 WebSocket disconnected manually', 'info');
            } else {
                log('No WebSocket to disconnect', 'warning');
            }
        }
        
        function testMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testMsg = {type: 'test', message: 'Hello from debug tool'};
                ws.send(JSON.stringify(testMsg));
                log('📤 Sent test message', 'info');
            } else {
                log('❌ WebSocket not connected', 'error');
            }
        }
        
        function triggerUpdate() {
            log('🚀 Triggering manual update via fetch...', 'info');
            
            fetch('/admin/queue_system/queuedjob/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({action: 'trigger_update'})
            })
            .then(response => {
                log(`📡 Update trigger response: ${response.status}`, response.ok ? 'success' : 'error');
            })
            .catch(error => {
                log(`❌ Update trigger failed: ${error}`, 'error');
            });
        }
        
        function checkGlobalSocket() {
            if (window.adminSocket) {
                const state = window.adminSocket.readyState;
                const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
                log(`🔍 Global WebSocket found: state=${states[state]}, url=${window.adminSocket.url}`, 'success');
            } else {
                log('❌ No global WebSocket found', 'error');
            }
        }
        
        function testQueueUpdate() {
            log('📊 Testing queue update simulation...', 'info');
            
            // Simulate the updateDashboard function
            const testData = {
                overview: {
                    queued: 1,
                    processing: 2,
                    completed: 3,
                    failed: 0,
                    review: 1
                }
            };
            
            log(`🧪 Simulating updateDashboard with: ${JSON.stringify(testData)}`, 'info');
            
            if (typeof updateDashboard === 'function') {
                updateDashboard(testData);
                log('✅ updateDashboard function called', 'success');
            } else {
                log('❌ updateDashboard function not found', 'error');
            }
        }
        
        function clearLogs() {
            document.getElementById('messages').innerHTML = '';
            messageCount = 0;
            log('🗑️ Logs cleared', 'info');
        }
        
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // Auto-connect on page load
        window.onload = function() {
            log('🚀 WebSocket Debug Tool loaded', 'info');
            log('💡 Click "Connect WebSocket" to start debugging', 'info');
        };
    </script>
</body>
</html>
