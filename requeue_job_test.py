#!/usr/bin/env python3
"""
Requeue an existing job to test the detailed error handling
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.models import QueuedJob
from config.celery import app as celery_app

def requeue_job(job_id):
    """Requeue a job for testing"""
    
    try:
        job = QueuedJob.objects.get(id=job_id)
        
        print(f"📋 Requeuing Job #{job.id}")
        print(f"Customer: {job.order.first_name} {job.order.surname}")
        print(f"Current Status: {job.status}")
        print(f"Current Error: {job.failure_reason}")
        
        # Reset job status for requeuing
        job.status = 'queued'
        job.started_at = None
        job.worker_id = None
        job.save()
        
        # Queue the job
        queue_name = f'location.{job.location.id}'
        
        result = celery_app.send_task(
            'queue_system.tasks.process_order',
            args=[str(job.order.id), str(job.location.id)],
            queue=queue_name
        )
        
        print(f"✅ Job requeued successfully!")
        print(f"📋 Task ID: {result.id}")
        print(f"🔄 Queue: {queue_name}")
        print(f"⏰ The job should be processed shortly...")
        
        print(f"\n🔗 Monitor progress:")
        print(f"   - Job Details: http://localhost:8000/queue/admin/job/{job.id}/")
        print(f"   - Admin List: http://localhost:8000/admin/queue_system/queuedjob/")
        
        return True
        
    except QueuedJob.DoesNotExist:
        print(f"❌ Job #{job_id} not found")
        return False
    except Exception as e:
        print(f"❌ Error requeuing job: {str(e)}")
        return False

def list_available_jobs():
    """List jobs that can be requeued"""
    
    print("📋 Available Jobs for Testing:")
    print("=" * 50)
    
    jobs = QueuedJob.objects.filter(status__in=['failed', 'review']).order_by('-created_at')[:5]
    
    if not jobs:
        print("No failed or review jobs found.")
        return []
    
    for job in jobs:
        print(f"#{job.id}: {job.order.first_name} {job.order.surname}")
        print(f"   Status: {job.status}")
        print(f"   Location: {job.location.location_name}")
        print(f"   Failure Reason: {job.failure_reason or 'Unknown'}")
        print(f"   Retry Count: {job.retry_count}/{job.max_retries}")
        print()
    
    return jobs

if __name__ == "__main__":
    print("🔄 Job Requeue Test")
    print("=" * 40)
    
    # List available jobs
    jobs = list_available_jobs()
    
    if not jobs:
        print("❌ No jobs available for testing")
        sys.exit(1)
    
    # Use the first available job
    job_to_test = jobs[0]
    
    print(f"🎯 Testing with Job #{job_to_test.id}")
    
    # Requeue the job
    success = requeue_job(job_to_test.id)
    
    if success:
        print(f"\n🎉 Job requeued successfully!")
        print(f"💡 The job will likely fail again and show detailed error descriptions")
        print(f"⏰ Check the job status in a minute to see the results")
    else:
        print(f"❌ Failed to requeue job")
