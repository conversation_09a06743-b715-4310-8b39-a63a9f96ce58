<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Live Updates Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .connected { background: #d4edda; color: #155724; }
        .connecting { background: #fff3cd; color: #856404; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            font-size: 12px;
            text-transform: uppercase;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 WebSocket Live Updates Test</h1>
        
        <div id="status" class="status connecting">🟡 Connecting to WebSocket...</div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-value" id="total-jobs">-</div>
                <div class="stat-label">Total Jobs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="queued-jobs">-</div>
                <div class="stat-label">Queued</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="processing-jobs">-</div>
                <div class="stat-label">Processing</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completed-jobs">-</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="failed-jobs">-</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="review-jobs">-</div>
                <div class="stat-label">Review</div>
            </div>
        </div>
        
        <div>
            <button onclick="connectWebSocket()" id="connectBtn">Connect</button>
            <button onclick="disconnectWebSocket()" id="disconnectBtn" disabled>Disconnect</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <h3>📋 Connection Log</h3>
        <div id="log" class="log"></div>
        
        <h3>📊 Recent Jobs</h3>
        <div id="recent-jobs" style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <em>No recent jobs data</em>
        </div>
    </div>

    <script>
        let socket = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = message;
        }

        function updateStats(data) {
            if (data.overview) {
                document.getElementById('total-jobs').textContent = data.overview.total_jobs || 0;
                document.getElementById('queued-jobs').textContent = data.overview.queued || 0;
                document.getElementById('processing-jobs').textContent = data.overview.processing || 0;
                document.getElementById('completed-jobs').textContent = data.overview.completed || 0;
                document.getElementById('failed-jobs').textContent = data.overview.failed || 0;
                document.getElementById('review-jobs').textContent = data.overview.review || 0;
            }
        }

        function updateRecentJobs(jobs) {
            const container = document.getElementById('recent-jobs');
            if (!jobs || jobs.length === 0) {
                container.innerHTML = '<em>No recent jobs</em>';
                return;
            }

            container.innerHTML = jobs.slice(0, 5).map(job => `
                <div style="padding: 8px; border-bottom: 1px solid #dee2e6; margin-bottom: 8px;">
                    <strong>${job.order__first_name} ${job.order__surname}</strong>
                    <span style="float: right; color: #6c757d;">${job.status}</span>
                    <br>
                    <small style="color: #6c757d;">
                        ${job.location__location_name} • ${job.retry_count}/${job.max_retries} retries
                    </small>
                </div>
            `).join('');
        }

        function connectWebSocket() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                log('⚠️ WebSocket already connected');
                return;
            }

            const wsUrl = 'ws://localhost:8000/ws/admin/live/';
            log(`🔗 Connecting to ${wsUrl}...`);
            updateStatus('connecting', '🟡 Connecting...');

            socket = new WebSocket(wsUrl);

            socket.onopen = function(event) {
                log('✅ WebSocket connected successfully');
                updateStatus('connected', '🟢 Connected - Live Updates Active');
                reconnectAttempts = 0;
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
            };

            socket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`📨 Received: ${data.type}`);
                    
                    if (data.type === 'initial_data' || data.type === 'admin_update') {
                        updateStats(data.data);
                        if (data.data.recent_jobs) {
                            updateRecentJobs(data.data.recent_jobs);
                        }
                        log(`📊 Updated stats: ${JSON.stringify(data.data.overview || {})}`);
                    }
                } catch (error) {
                    log(`❌ Error parsing message: ${error.message}`);
                }
            };

            socket.onclose = function(event) {
                log(`🔌 WebSocket closed (code: ${event.code})`);
                updateStatus('disconnected', '🔴 Disconnected');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;

                if (reconnectAttempts < maxReconnectAttempts && event.code !== 1000) {
                    reconnectAttempts++;
                    log(`🔄 Attempting to reconnect (${reconnectAttempts}/${maxReconnectAttempts})...`);
                    setTimeout(connectWebSocket, 3000);
                }
            };

            socket.onerror = function(error) {
                log(`❌ WebSocket error: ${error.message || 'Connection failed'}`);
                updateStatus('disconnected', '🔴 Connection Error');
            };
        }

        function disconnectWebSocket() {
            if (socket) {
                socket.close(1000, 'Manual disconnect');
                socket = null;
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Auto-connect on page load
        window.addEventListener('load', function() {
            log('🚀 Page loaded, attempting to connect...');
            connectWebSocket();
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (socket) {
                socket.close();
            }
        });
    </script>
</body>
</html>
