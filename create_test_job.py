#!/usr/bin/env python3
"""
Create a test job that will demonstrate the detailed error descriptions
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from orders.models import Order, BarbadosForm
from queue_system.models import QueuedJob, Location
from django.utils import timezone
from datetime import datetime, timedelta

def create_test_order():
    """Create a test order with Barbados form data"""
    
    # Get Barbados location
    barbados_location = Location.objects.filter(location_name__icontains='barbados').first()
    if not barbados_location:
        print("❌ No Barbados location found")
        return None
    
    # Create a test order
    order = Order.objects.create(
        first_name="Test",
        surname="User",
        email="<EMAIL>",
        phone_number="+1234567890",
        location=barbados_location,
        arrival_date=timezone.now().date() + timedelta(days=30),
        departure_date=timezone.now().date() + timedelta(days=37),
        status='criminal_check_passed'
    )
    
    # Create Barbados form data
    barbados_form = BarbadosForm.objects.create(
        order=order,
        gender='Male',
        date_of_birth=datetime(1990, 1, 1).date(),
        place_of_birth='Test City',
        nationality='Test Country',
        passport_number='TEST123456',
        passport_issue_date=datetime(2020, 1, 1).date(),
        passport_expiry_date=datetime(2030, 1, 1).date(),
        passport_issuing_authority='Test Authority',
        occupation='Software Developer',
        purpose_of_visit='Tourism',
        address_in_barbados='Test Hotel, Test Street',
        flight_number='TEST123',
        port_of_embarkation='Test Airport'
    )
    
    print(f"✅ Created test order #{order.id}")
    print(f"📋 Customer: {order.first_name} {order.surname}")
    print(f"📍 Location: {order.location.location_name}")
    print(f"📝 Barbados form created: {barbados_form.id}")
    
    return order

def create_test_job(order):
    """Create a test job for the order"""
    
    job = QueuedJob.objects.create(
        order=order,
        location=order.location,
        status='queued',
        priority='normal',
        max_retries=3
    )
    
    print(f"✅ Created test job #{job.id}")
    print(f"📋 Status: {job.status}")
    print(f"🔄 Max retries: {job.max_retries}")
    
    return job

def queue_test_job(job):
    """Queue the test job for processing"""
    
    try:
        from config.celery import app as celery_app
        
        queue_name = f'location.{job.location.id}'
        
        # Send the task to Celery
        result = celery_app.send_task(
            'queue_system.tasks.process_order',
            args=[str(job.order.id), str(job.location.id)],
            queue=queue_name
        )
        
        print(f"✅ Job queued successfully")
        print(f"📋 Task ID: {result.id}")
        print(f"🔄 Queue: {queue_name}")
        print(f"🎯 This job will fail and demonstrate detailed error descriptions")
        
        return result
        
    except Exception as e:
        print(f"❌ Failed to queue job: {str(e)}")
        return None

if __name__ == "__main__":
    print("🚀 Creating Test Job for Error Demonstration")
    print("=" * 60)
    
    # Create test order
    order = create_test_order()
    if not order:
        print("❌ Failed to create test order")
        sys.exit(1)
    
    # Create test job
    job = create_test_job(order)
    
    # Queue the job
    result = queue_test_job(job)
    
    if result:
        print(f"\n🎉 Test job created and queued!")
        print(f"🔗 Monitor progress:")
        print(f"   - Job Details: http://localhost:8000/queue/admin/job/{job.id}/")
        print(f"   - Admin List: http://localhost:8000/admin/queue_system/queuedjob/")
        print(f"   - Queue Overview: http://localhost:8000/queue/admin/")
        
        print(f"\n💡 The job will likely fail because:")
        print(f"   - The bot will try to fill a real form")
        print(f"   - This will trigger detailed error descriptions")
        print(f"   - You'll see specific failure reasons instead of 'unknown_error'")
        
        print(f"\n⏰ Check the job status in a few minutes to see the detailed error!")
    else:
        print("❌ Failed to queue test job")
