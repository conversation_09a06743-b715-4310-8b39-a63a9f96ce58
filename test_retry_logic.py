#!/usr/bin/env python
"""
Test script to verify the retry logic is working correctly
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.models import QueuedJob, JobError
from orders.models import order as Order
from locations.models import Location
from django.utils import timezone

def test_retry_logic():
    """Test the retry counting logic"""
    print("🔍 Testing retry logic...")
    
    # Get a test order and location
    try:
        test_order = Order.objects.first()
        test_location = Location.objects.first()
        
        if not test_order or not test_location:
            print("❌ No test data found")
            return False
            
        # Create a test job
        job = QueuedJob.objects.create(
            order=test_order,
            location=test_location,
            status='queued',
            max_retries=3
        )
        
        print(f"Created test job {job.id} with max_retries={job.max_retries}")
        print(f"Initial retry_count: {job.retry_count}")
        
        # Test retry logic
        for attempt in range(1, 6):  # Try 5 times to see what happens
            print(f"\n--- Attempt {attempt} ---")
            print(f"Before: retry_count={job.retry_count}, max_retries={job.max_retries}")
            
            # Check if we should retry
            if job.retry_count < job.max_retries:
                print("✅ Should retry - incrementing retry_count")
                job.retry_count += 1
                job.status = 'queued'
                job.save()
                print(f"After increment: retry_count={job.retry_count}")
            else:
                print("❌ Max retries reached - marking as failed")
                job.mark_as_failed_final(
                    error_message=f"Test failure after {job.retry_count} retries",
                    failure_reason="test_error"
                )
                print(f"Final state: retry_count={job.retry_count}, status={job.status}")
                break
                
        # Clean up
        job.delete()
        print(f"\n✅ Test completed - cleaned up job {job.id}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mark_as_failed_methods():
    """Test the difference between mark_as_failed and mark_as_failed_final"""
    print("\n🔍 Testing mark_as_failed methods...")
    
    try:
        test_order = Order.objects.first()
        test_location = Location.objects.first()
        
        # Test mark_as_failed (should increment retry_count)
        job1 = QueuedJob.objects.create(
            order=test_order,
            location=test_location,
            status='processing',
            retry_count=1,
            max_retries=3
        )
        
        print(f"Job1 before mark_as_failed: retry_count={job1.retry_count}")
        job1.mark_as_failed(error_message="Test error", failure_reason="test")
        print(f"Job1 after mark_as_failed: retry_count={job1.retry_count}")
        
        # Test mark_as_failed_final (should NOT increment retry_count)
        job2 = QueuedJob.objects.create(
            order=test_order,
            location=test_location,
            status='processing',
            retry_count=3,
            max_retries=3
        )
        
        print(f"Job2 before mark_as_failed_final: retry_count={job2.retry_count}")
        job2.mark_as_failed_final(error_message="Final test error", failure_reason="test")
        print(f"Job2 after mark_as_failed_final: retry_count={job2.retry_count}")
        
        # Clean up
        job1.delete()
        job2.delete()
        print("✅ mark_as_failed methods test completed")
        return True
        
    except Exception as e:
        print(f"❌ mark_as_failed test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Retry Logic Fix")
    print("=" * 50)
    
    test1_ok = test_retry_logic()
    test2_ok = test_mark_as_failed_methods()
    
    if test1_ok and test2_ok:
        print("\n🎉 All retry logic tests passed!")
        print("The retry counting should now be correct (max 3/3, not 4/3)")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
