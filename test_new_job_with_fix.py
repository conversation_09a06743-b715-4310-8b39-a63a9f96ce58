#!/usr/bin/env python3
"""
Test creating a new job to verify detailed error descriptions work
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from orders.models import order, BarbadosForm
from queue_system.models import QueuedJob, Location, JobError
from django.utils import timezone
from datetime import datetime, timedelta
from config.celery import app as celery_app

def create_test_job():
    """Create a test job to verify error handling"""
    
    # Get an existing order to avoid creating duplicate data
    existing_order = order.objects.filter(status='criminal_check_passed').first()
    
    if existing_order:
        print(f"📋 Using existing order: {existing_order.id}")
        print(f"Customer: {existing_order.first_name} {existing_order.surname}")
        
        # Create a new job for this order
        job = QueuedJob.objects.create(
            order=existing_order,
            location=existing_order.location,
            status='queued',
            priority_flag=False,
            max_retries=3
        )
        
        print(f"✅ Created new job #{job.id}")
        return job
    else:
        print("❌ No suitable existing order found")
        return None

def test_job_processing(job):
    """Test job processing to see if detailed errors are created"""
    
    print(f"\n🔄 Testing job #{job.id} processing...")
    
    # Queue the job
    queue_name = f'location.{job.location.id}'
    
    try:
        result = celery_app.send_task(
            'queue_system.tasks.process_order',
            args=[str(job.order.id), str(job.location.id)],
            queue=queue_name
        )
        
        print(f"✅ Job queued with task ID: {result.id}")
        print(f"🔄 Queue: {queue_name}")
        print(f"⏰ Waiting for processing...")
        
        return result
        
    except Exception as e:
        print(f"❌ Failed to queue job: {str(e)}")
        return None

def check_job_results(job_id, wait_time=60):
    """Check job results after processing"""
    
    import time
    
    print(f"\n⏰ Waiting {wait_time} seconds for job to process...")
    time.sleep(wait_time)
    
    try:
        job = QueuedJob.objects.get(id=job_id)
        
        print(f"\n📊 Job #{job.id} Results:")
        print(f"  Status: {job.status}")
        print(f"  Failure Reason: {job.failure_reason}")
        print(f"  Error Message: {job.error_message}")
        print(f"  Retry Count: {job.retry_count}/{job.max_retries}")
        
        # Check JobError records
        errors = JobError.objects.filter(job=job).order_by('-occurred_at')
        print(f"\n📋 JobError Records: {errors.count()}")
        
        if errors.count() > 0:
            print("✅ JobError records found - detailed error handling is working!")
            for i, error in enumerate(errors[:2]):
                print(f"  Error {i+1}:")
                print(f"    Message: {error.error_message}")
                print(f"    Occurred: {error.occurred_at}")
                if error.error_details:
                    print(f"    Details: {error.error_details}")
                print()
        else:
            print("❌ No JobError records found")
            
        # Check if we have detailed error descriptions
        if job.failure_reason != 'unknown_error':
            print("✅ Specific failure reason found!")
        else:
            print("❌ Still showing generic 'unknown_error'")
            
        if '[SUCCESS]' in job.error_message or '[FAILED]' in job.error_message:
            print("✅ Detailed error description found!")
        else:
            print("❌ Still showing generic error message")
            
        return job
        
    except QueuedJob.DoesNotExist:
        print(f"❌ Job #{job_id} not found")
        return None

if __name__ == "__main__":
    print("🧪 Testing New Job with Detailed Error Fix")
    print("=" * 60)
    
    # Create test job
    job = create_test_job()
    
    if not job:
        print("❌ Failed to create test job")
        sys.exit(1)
    
    # Test job processing
    result = test_job_processing(job)
    
    if result:
        # Check results
        final_job = check_job_results(job.id)
        
        if final_job:
            print(f"\n🔗 View Results:")
            print(f"  Job Details: http://localhost:8000/queue/admin/job/{final_job.id}/")
            print(f"  Admin View: http://localhost:8000/admin/queue_system/queuedjob/{final_job.id}/change/")
            
            # Summary
            if (final_job.failure_reason != 'unknown_error' and 
                JobError.objects.filter(job=final_job).exists()):
                print(f"\n🎉 SUCCESS! Detailed error descriptions are working!")
            else:
                print(f"\n❌ FAILED! Still getting generic errors")
        
    print(f"\n🎉 Test complete!")
