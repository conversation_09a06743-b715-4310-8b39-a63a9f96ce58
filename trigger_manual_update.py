#!/usr/bin/env python
"""
Manually trigger WebSocket updates to test the system
"""
import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def trigger_update():
    """Manually trigger a WebSocket update"""
    setup_django()
    
    print("🚀 MANUALLY TRIGGERING WEBSOCKET UPDATE")
    print("=" * 60)
    
    try:
        from queue_system.signals import send_admin_live_update
        
        print("📡 Calling send_admin_live_update() directly...")
        send_admin_live_update()
        print("✅ Update signal sent!")
        
        print("\n📋 What should happen:")
        print("  1. Signal sends message to 'admin_updates' group")
        print("  2. Consumer receives 'admin_update' message")
        print("  3. Consumer sends data to WebSocket clients")
        print("  4. <PERSON><PERSON><PERSON> receives and processes update")
        print("  5. Queue statistics update in UI")
        
        print("\n👀 Check browser console for:")
        print("  📨 Global WebSocket received message: ...")
        print("  📊 Queue page processing WebSocket data: admin_update")
        print("  🎯 updateDashboard called with data: ...")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to trigger update: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_current_data():
    """Check current queue data"""
    setup_django()
    
    print("\n📊 CURRENT QUEUE DATA")
    print("=" * 60)
    
    try:
        from queue_system.models import QueuedJob
        
        total = QueuedJob.objects.count()
        queued = QueuedJob.objects.filter(status='queued').count()
        processing = QueuedJob.objects.filter(status='processing').count()
        completed = QueuedJob.objects.filter(status='completed').count()
        failed = QueuedJob.objects.filter(status='failed').count()
        review = QueuedJob.objects.filter(status='review').count()
        
        print(f"📈 Total Jobs: {total}")
        print(f"⏳ Queued: {queued}")
        print(f"🔄 Processing: {processing}")
        print(f"✅ Completed: {completed}")
        print(f"❌ Failed: {failed}")
        print(f"🔍 Review: {review}")
        
        print(f"\n💡 Expected UI values:")
        print(f"  QUEUED: {queued}")
        print(f"  PROCESSING: {processing}")
        print(f"  COMPLETED: {completed}")
        print(f"  FAILED: {failed}")
        print(f"  UNDER REVIEW: {review}")
        print(f"  REQUEUED: 0")  # This might need calculation
        print(f"  PRIORITY: 0")  # This might need calculation
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to get current data: {e}")
        return False

def test_job_update():
    """Test updating a job to trigger signals"""
    setup_django()
    
    print("\n🧪 TESTING JOB UPDATE")
    print("=" * 60)
    
    try:
        from queue_system.models import QueuedJob
        
        jobs = QueuedJob.objects.all()[:1]
        if not jobs:
            print("❌ No jobs found to update")
            return False
            
        job = jobs[0]
        original_status = job.status
        
        print(f"📝 Job {job.id} current status: {original_status}")
        
        # Change status to trigger signal
        new_status = 'processing' if original_status != 'processing' else 'queued'
        job.status = new_status
        job.save()
        
        print(f"✅ Updated job {job.id}: {original_status} → {new_status}")
        print("📡 This should trigger admin_update signal automatically")
        
        # Wait a moment then change back
        import time
        time.sleep(2)
        
        job.status = original_status
        job.save()
        
        print(f"🔄 Reverted job {job.id}: {new_status} → {original_status}")
        print("📡 This should trigger another admin_update signal")
        
        return True
        
    except Exception as e:
        print(f"❌ Job update test failed: {e}")
        return False

def main():
    print("🎯 MANUAL WEBSOCKET UPDATE TRIGGER")
    print("=" * 70)
    
    print("📋 Instructions:")
    print("1. Make sure Django server is running")
    print("2. Open queue admin page in browser")
    print("3. Open browser console (F12)")
    print("4. Run this script")
    print("5. Watch console for WebSocket messages")
    print("6. Check if queue statistics update")
    
    # Check current data first
    data_ok = check_current_data()
    
    # Trigger manual update
    if data_ok:
        print("\n" + "="*70)
        trigger_ok = trigger_update()
        
        # Test job update
        if trigger_ok:
            print("\n" + "="*70)
            job_ok = test_job_update()
        else:
            job_ok = False
    else:
        trigger_ok = False
        job_ok = False
    
    print("\n📊 TEST SUMMARY")
    print("=" * 70)
    print(f"Current Data: {'✅ PASS' if data_ok else '❌ FAIL'}")
    print(f"Manual Trigger: {'✅ PASS' if trigger_ok else '❌ FAIL'}")
    print(f"Job Update: {'✅ PASS' if job_ok else '❌ FAIL'}")
    
    if all([data_ok, trigger_ok, job_ok]):
        print("\n🎉 All tests passed!")
        print("\n📋 If queue statistics still don't update:")
        print("  1. Check browser console for WebSocket messages")
        print("  2. Verify Django console shows no errors")
        print("  3. Check if global WebSocket is actually connected")
        print("  4. Try refreshing the page")
    else:
        print("\n⚠️ Some tests failed - check the errors above")
        
    print("\n🔍 Next: Check browser console for these messages:")
    print("  📨 Global WebSocket received message")
    print("  📊 Queue page processing WebSocket data")
    print("  🎯 updateDashboard called with data")

if __name__ == "__main__":
    main()
