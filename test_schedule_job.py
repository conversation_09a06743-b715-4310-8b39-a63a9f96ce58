#!/usr/bin/env python
"""
Test script to verify the schedule_job task works with <PERSON><PERSON>
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from config.celery import app as celery_app
from queue_system.tasks import schedule_job
from orders.models import order as Order
import uuid

def test_schedule_job_direct():
    """Test calling schedule_job directly"""
    print("🔍 Testing schedule_job task directly...")
    
    # Get a test order
    try:
        test_order = Order.objects.first()
        if not test_order:
            print("❌ No orders found in database")
            return False
            
        print(f"Found test order: {test_order.id}")
        
        # Call schedule_job directly (this will use the updated celery_app.send_task)
        result = schedule_job(str(test_order.id))
        print(f"✅ schedule_job completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ schedule_job failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_schedule_job_async():
    """Test sending schedule_job to queue"""
    print("\n🔍 Testing schedule_job via queue...")
    
    try:
        # Get a test order
        test_order = Order.objects.first()
        if not test_order:
            print("❌ No orders found in database")
            return False
            
        print(f"Found test order: {test_order.id}")
        
        # Send schedule_job to queue using the configured app
        result = celery_app.send_task(
            'queue_system.tasks.schedule_job',
            args=[str(test_order.id)],
            queue='scheduler'
        )
        print(f"✅ schedule_job sent to queue successfully: {result.id}")
        return True
        
    except Exception as e:
        print(f"❌ schedule_job queue sending failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing schedule_job with Redis Configuration")
    print("=" * 60)
    
    # Test direct call
    direct_ok = test_schedule_job_direct()
    
    # Test async call
    async_ok = test_schedule_job_async()
    
    if direct_ok and async_ok:
        print("\n🎉 All schedule_job tests passed!")
        print("The queue system should now work correctly with Redis.")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
