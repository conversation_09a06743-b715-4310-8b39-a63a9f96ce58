#!/usr/bin/env python
"""
Test script to verify WebSocket functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

def test_channel_layer():
    """Test if channel layer is configured correctly"""
    print("🔍 Testing channel layer configuration...")
    try:
        from channels.layers import get_channel_layer
        channel_layer = get_channel_layer()
        
        if channel_layer is None:
            print("❌ Channel layer is None")
            return False
            
        print(f"✅ Channel layer configured: {type(channel_layer).__name__}")
        print(f"   Backend: {channel_layer.__class__.__module__}")
        return True
        
    except Exception as e:
        print(f"❌ Channel layer test failed: {e}")
        return False

def test_redis_connection():
    """Test Redis connection for channels"""
    print("\n🔍 Testing Redis connection for channels...")
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        result = r.ping()
        print(f"✅ Redis ping successful: {result}")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

def test_consumer_import():
    """Test if consumers can be imported"""
    print("\n🔍 Testing consumer imports...")
    try:
        from queue_system.consumer import QueueUpdatesConsumer, AdminLiveUpdatesConsumer
        print("✅ QueueUpdatesConsumer imported successfully")
        print("✅ AdminLiveUpdatesConsumer imported successfully")
        return True
    except Exception as e:
        print(f"❌ Consumer import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_routing():
    """Test WebSocket routing"""
    print("\n🔍 Testing WebSocket routing...")
    try:
        from queue_system.routing import websocket_urlpatterns
        print(f"✅ WebSocket URL patterns loaded: {len(websocket_urlpatterns)} patterns")
        for pattern in websocket_urlpatterns:
            print(f"   - {pattern.pattern.regex.pattern}")
        return True
    except Exception as e:
        print(f"❌ Routing test failed: {e}")
        return False

def test_asgi_application():
    """Test ASGI application"""
    print("\n🔍 Testing ASGI application...")
    try:
        from config.asgi import application
        print(f"✅ ASGI application loaded: {type(application).__name__}")
        return True
    except Exception as e:
        print(f"❌ ASGI application test failed: {e}")
        return False

def test_signals():
    """Test if signals are working"""
    print("\n🔍 Testing signal functionality...")
    try:
        from queue_system.signals import send_admin_live_update
        print("✅ send_admin_live_update function imported")
        
        # Test calling the function (should not crash)
        send_admin_live_update()
        print("✅ send_admin_live_update executed without errors")
        return True
    except Exception as e:
        print(f"❌ Signals test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing WebSocket Live Updates Setup")
    print("=" * 60)
    
    tests = [
        test_redis_connection,
        test_channel_layer,
        test_consumer_import,
        test_routing,
        test_asgi_application,
        test_signals
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! WebSocket live updates should work.")
        print("\n📋 Next steps:")
        print("1. Start Django with ASGI: python manage.py runserver")
        print("2. Open admin interface in browser")
        print("3. Look for live indicator in top-right corner")
        print("4. Create/update jobs to see live updates")
    else:
        print("❌ Some tests failed. Check the errors above.")
