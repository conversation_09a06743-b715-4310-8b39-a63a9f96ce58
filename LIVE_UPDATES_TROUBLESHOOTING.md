# Live Updates Troubleshooting Guide

## 🚨 Current Issue: Admin UI Not Getting Real-Time Updates

The WebSocket live updates system has been implemented but the admin UI is not receiving real-time updates. Here's how to diagnose and fix the issue.

## 🔍 Diagnosis Steps

### Step 1: Check Django Server
The main issue appears to be that something is intercepting the `python manage.py runserver` command and starting Celery workers instead of the Django server.

**Test:**
```bash
# Try starting Django server directly
python start_django_asgi.py
```

### Step 2: Verify WebSocket Connection
Open the test file in your browser:
```
file:///path/to/test_websocket.html
```

**Expected behavior:**
- Should show "🟡 Connecting..." then "🔴 Connection Error" (if server not running)
- Should show "🟢 Connected" if WebSocket server is running

### Step 3: Check Browser Console
1. Open admin interface in browser
2. Press F12 to open Developer Tools
3. Go to Console tab
4. Look for WebSocket connection messages

**Expected messages:**
```
🚀 Initializing Live Updates...
🔗 Connecting to WebSocket: ws://localhost:8000/ws/admin/live/
✅ Admin WebSocket connected
```

## 🛠️ Solutions

### Solution 1: Start Django with ASGI Support
```bash
# Option A: Use the custom startup script
python start_django_asgi.py

# Option B: Install uvicorn and start directly
pip install uvicorn
uvicorn config.asgi:application --host 127.0.0.1 --port 8000 --reload

# Option C: Install daphne and start
pip install daphne
daphne -b 127.0.0.1 -p 8000 config.asgi:application
```

### Solution 2: Fix Django Server Override
Something in your setup is overriding the runserver command. Check:

1. **Environment variables:**
   ```bash
   echo %DJANGO_SETTINGS_MODULE%
   ```

2. **Custom management commands:**
   ```bash
   # Check if there's a custom runserver command
   python manage.py help runserver
   ```

3. **Apps.py auto-start:**
   - Already checked - auto-start is commented out

### Solution 3: Manual WebSocket Test
1. Start Django server (any method that works)
2. Open `test_websocket.html` in browser
3. Check if WebSocket connects

### Solution 4: Verify Template Loading
Check if the admin template override is working:

1. **Add debug to template:**
   ```html
   <!-- Add this to templates/admin/base_site.html -->
   <script>console.log('🎯 Live updates template loaded!');</script>
   ```

2. **Check static files:**
   ```bash
   python manage.py collectstatic
   ```

## 🔧 Quick Fixes

### Fix 1: Force WebSocket Connection
Add this to any admin page manually:

```html
<script>
// Add to browser console or admin template
const ws = new WebSocket('ws://localhost:8000/ws/admin/live/');
ws.onopen = () => console.log('✅ WebSocket connected');
ws.onmessage = (e) => console.log('📨 Message:', JSON.parse(e.data));
ws.onerror = (e) => console.log('❌ WebSocket error:', e);
</script>
```

### Fix 2: Test Signal Manually
```bash
# Test if signals work
python manage.py shell
>>> from queue_system.signals import send_admin_live_update
>>> send_admin_live_update()
```

### Fix 3: Check Redis Connection
```bash
# Test Redis
python -c "import redis; r=redis.Redis(); print('Redis:', r.ping())"
```

## 📊 Expected Behavior

When working correctly, you should see:

1. **Live Indicator:** Top-right corner showing connection status
2. **Stats Widget:** Click indicator to show/hide live stats
3. **Real-time Updates:** Numbers change without page refresh
4. **Console Messages:** WebSocket connection and update messages

## 🐛 Common Issues

### Issue 1: WebSocket Connection Refused
**Cause:** Django server not running with ASGI support
**Fix:** Use `python start_django_asgi.py`

### Issue 2: No Live Indicator Visible
**Cause:** JavaScript not loading or template not overriding
**Fix:** Check browser console for errors, verify static files

### Issue 3: Connected but No Updates
**Cause:** Signals not triggering or channel layer not working
**Fix:** Test signals manually, check Redis connection

### Issue 4: Template Not Loading
**Cause:** Django not using custom admin template
**Fix:** Verify `templates/admin/base_site.html` is in TEMPLATES path

## 🎯 Next Steps

1. **Start Django server properly:**
   ```bash
   python start_django_asgi.py
   ```

2. **Open admin interface and check for:**
   - Live indicator in top-right corner
   - Console messages about WebSocket connection

3. **Test live updates:**
   ```bash
   # In another terminal
   python manage.py test_live_updates
   ```

4. **Create/update a job and watch for real-time changes**

## 📞 If Still Not Working

1. **Check browser console for errors**
2. **Verify Django server is running on port 8000**
3. **Test WebSocket connection with `test_websocket.html`**
4. **Check if Redis is running: `redis-cli ping`**
5. **Verify channels is installed: `pip list | grep channels`**

The live updates system is fully implemented - the main issue is getting Django to run with ASGI support instead of starting Celery workers.
