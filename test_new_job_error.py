#!/usr/bin/env python3
"""
Test script to simulate a new job failure and verify detailed error descriptions work
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.models import QueuedJob, JobError
from queue_system.tasks import parse_bot_error
from django.utils import timezone
import traceback

def simulate_bot_error():
    """Simulate a bot error and test the parsing"""
    
    # Simulate different types of bot errors
    test_errors = [
        {
            'error': "Error clicking element: Unable to select gender from the drop down menu",
            'trace': """Traceback (most recent call last):
  File 'barbados_form_1.py', line 45, in fill_form
    select_gender(driver, 'Male')
  File 'barbados_form_1.py', line 78, in select_gender
    raise Exception('Unable to select gender from the drop down menu')
Exception: Unable to select gender from the drop down menu"""
        },
        {
            'error': "TimeoutException: Page loading timeout after 30 seconds",
            'trace': """Traceback (most recent call last):
  File 'barbados_form_1.py', line 25, in navigate_to_form
    WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.ID, "form-container")))
selenium.common.exceptions.TimeoutException: Message: Page loading timeout after 30 seconds"""
        },
        {
            'error': "NoSuchElementException: Unable to locate element: {'method': 'id', 'selector': 'first-name'}",
            'trace': """Traceback (most recent call last):
  File 'barbados_form_1.py', line 55, in fill_personal_info
    first_name_field = driver.find_element(By.ID, "first-name")
selenium.common.exceptions.NoSuchElementException: Unable to locate element: {'method': 'id', 'selector': 'first-name'}"""
        }
    ]
    
    print("🧪 Testing Bot Error Parsing")
    print("=" * 50)
    
    for i, test_case in enumerate(test_errors, 1):
        print(f"\n📋 Test Case {i}:")
        print(f"Error: {test_case['error']}")
        
        # Parse the error
        error_msg, detailed_description, failure_reason = parse_bot_error(
            test_case['error'], 
            test_case['trace']
        )
        
        print(f"\n✅ Parsed Results:")
        print(f"Error Message: {error_msg}")
        print(f"Failure Reason: {failure_reason}")
        print(f"Detailed Description:")
        print(detailed_description)
        print("-" * 30)

def test_with_real_job():
    """Test with a real job from the database"""
    
    print("\n🔍 Testing with Real Job")
    print("=" * 50)
    
    # Find a job to test with
    job = QueuedJob.objects.filter(status__in=['failed', 'review']).first()
    
    if not job:
        print("❌ No failed jobs found to test with")
        return
    
    print(f"📋 Testing with Job #{job.id}")
    print(f"Customer: {job.order.first_name} {job.order.surname}")
    print(f"Current Status: {job.status}")
    print(f"Current Failure Reason: {job.failure_reason}")
    
    # Simulate a gender dropdown error for this job
    error_message = "Error clicking element: Unable to select gender from the drop down menu"
    error_trace = """Traceback (most recent call last):
  File 'barbados_form_1.py', line 45, in fill_form
    select_gender(driver, 'Male')
  File 'barbados_form_1.py', line 78, in select_gender
    raise Exception('Unable to select gender from the drop down menu')
Exception: Unable to select gender from the drop down menu"""
    
    # Parse the error
    error_msg, detailed_description, failure_reason = parse_bot_error(error_message, error_trace)
    
    print(f"\n🔧 Creating New JobError Record:")
    
    # Create detailed error record
    error_details = {
        'error_type': 'Exception',
        'error_message': error_message,
        'detailed_description': detailed_description,
        'failure_reason': failure_reason,
        'order_id': str(job.order.id),
        'location': job.location.location_name,
        'timestamp': timezone.now().isoformat(),
        'bot_module': 'Barbados_form_1',
        'context': 'Bot execution failed during form processing'
    }
    
    # Create the JobError record
    job_error = JobError.objects.create(
        job=job,
        error_message=detailed_description,
        error_trace=error_trace,
        error_details=error_details
    )
    
    # Update job with detailed information
    job.error_message = detailed_description
    job.failure_reason = failure_reason
    job.save()
    
    print(f"✅ JobError #{job_error.id} created")
    print(f"📋 Job #{job.id} updated with:")
    print(f"   - Error Message: {detailed_description}")
    print(f"   - Failure Reason: {failure_reason}")
    
    print(f"\n🔗 View Results:")
    print(f"   - Job Details: http://localhost:8000/queue/admin/job/{job.id}/")
    print(f"   - Admin View: http://localhost:8000/admin/queue_system/queuedjob/{job.id}/change/")

if __name__ == "__main__":
    print("🚀 Testing Detailed Error Descriptions")
    print("=" * 60)
    
    # Test the parsing function
    simulate_bot_error()
    
    # Test with a real job
    test_with_real_job()
    
    print(f"\n🎉 Testing Complete!")
    print("💡 Now run a new job to see if the detailed errors appear automatically")
