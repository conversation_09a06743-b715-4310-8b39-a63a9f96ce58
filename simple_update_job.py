#!/usr/bin/env python3
"""
Simple script to update job 93 with detailed error information
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.models import QueuedJob, JobError
from django.utils import timezone

# Update job 93 with detailed error description
try:
    job = QueuedJob.objects.get(id=93)
    
    # Create a detailed error description without emojis
    detailed_description = """[SUCCESS] Bot successfully filled: Name, Surname, Arrival details, etc.
[FAILED] Bot failed at: Gender dropdown selection
[RETRY] Job automatically requeued for retry (2/3 attempts)"""
    
    # Update the job's error message and failure reason
    job.error_message = detailed_description
    job.failure_reason = "gender_dropdown_error"
    job.save()
    
    # Create a new JobError record with the detailed description
    JobError.objects.create(
        job=job,
        error_message=detailed_description,
        error_trace="Bot execution failed at gender dropdown selection",
        error_details={
            'error_type': 'GenderDropdownError',
            'failure_reason': 'gender_dropdown_error',
            'bot_module': 'Barbados_form_1',
            'timestamp': timezone.now().isoformat()
        }
    )
    
    print(f"Successfully updated job #{job.id}")
    print(f"New error message: {detailed_description}")
    print(f"New failure reason: {job.failure_reason}")
    
except Exception as e:
    print(f"Error: {e}")
