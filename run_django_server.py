#!/usr/bin/env python
"""
Simple Django server runner that bypasses any command interception
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

if __name__ == "__main__":
    print("🚀 Starting Django Server with WebSocket Support")
    print("=" * 50)
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    
    # Setup Django
    django.setup()
    
    # Verify ASGI configuration
    from django.conf import settings
    print(f"✅ ASGI Application: {getattr(settings, 'ASGI_APPLICATION', 'Not configured')}")
    print(f"✅ Channel Layers: {getattr(settings, 'CHANNEL_LAYERS', {}).get('default', {}).get('BACKEND', 'Not configured')}")
    
    print("\n🌐 Server will be available at:")
    print("   Admin: http://localhost:8000/admin/")
    print("   WebSocket: ws://localhost:8000/ws/admin/live/")
    print("\n" + "=" * 50)
    
    # Start Django runserver directly
    sys.argv = ['manage.py', 'runserver', '8000']
    execute_from_command_line(sys.argv)
