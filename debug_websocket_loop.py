#!/usr/bin/env python
"""
Debug WebSocket connection loop issues
"""
import os
import sys
import django
import time
import threading
from datetime import datetime

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def monitor_django_logs():
    """Monitor Django console output for WebSocket connection logs"""
    print("🔍 Monitoring Django logs for WebSocket connections...")
    print("Look for these messages in your Django server console:")
    print("  🔗 WebSocket connection attempt from user: ...")
    print("  ✅ WebSocket accepted for admin user: ...")
    print("  🔌 WebSocket disconnected: user=..., code=...")
    print("  ❌ WebSocket rejected: ...")
    print("\nIf you see rapid connect/disconnect cycles, that indicates the loop issue.")

def check_session_middleware():
    """Check if session middleware is properly configured"""
    setup_django()
    
    from django.conf import settings
    middleware = settings.MIDDLEWARE
    
    print("🔍 Checking session middleware configuration...")
    
    session_middleware = 'django.contrib.sessions.middleware.SessionMiddleware'
    auth_middleware = 'django.contrib.auth.middleware.AuthenticationMiddleware'
    
    if session_middleware in middleware:
        print(f"✅ Session middleware found at position {middleware.index(session_middleware)}")
    else:
        print("❌ Session middleware missing!")
        
    if auth_middleware in middleware:
        print(f"✅ Auth middleware found at position {middleware.index(auth_middleware)}")
    else:
        print("❌ Auth middleware missing!")
    
    # Check order
    if session_middleware in middleware and auth_middleware in middleware:
        session_pos = middleware.index(session_middleware)
        auth_pos = middleware.index(auth_middleware)
        if session_pos < auth_pos:
            print("✅ Middleware order correct (Session before Auth)")
        else:
            print("❌ Middleware order incorrect (Auth should come after Session)")

def check_channels_auth():
    """Check Channels authentication configuration"""
    setup_django()
    
    print("🔍 Checking Channels authentication...")
    
    try:
        from channels.auth import AuthMiddlewareStack
        print("✅ AuthMiddlewareStack available")
        
        # Check ASGI configuration
        from config.asgi import application
        print("✅ ASGI application loaded")
        
    except Exception as e:
        print(f"❌ Channels auth error: {e}")

def simulate_connection_test():
    """Simulate what happens during a WebSocket connection"""
    setup_django()
    
    print("🔍 Simulating WebSocket connection process...")
    
    try:
        # Test database queries that happen during connection
        from queue_system.models import QueuedJob
        
        print("📊 Testing database queries...")
        total_jobs = QueuedJob.objects.count()
        queued_jobs = QueuedJob.objects.filter(status='queued').count()
        processing_jobs = QueuedJob.objects.filter(status='processing').count()
        
        print(f"✅ Database queries successful: {total_jobs} total, {queued_jobs} queued, {processing_jobs} processing")
        
        # Test channel layer
        from channels.layers import get_channel_layer
        channel_layer = get_channel_layer()
        
        if channel_layer:
            print("✅ Channel layer accessible")
        else:
            print("❌ Channel layer not available")
            
    except Exception as e:
        print(f"❌ Connection simulation failed: {e}")
        import traceback
        traceback.print_exc()

def check_browser_issues():
    """Check for common browser-side issues"""
    print("🔍 Common browser-side issues that cause connection loops:")
    print()
    print("1. **Multiple JavaScript files loading:**")
    print("   - Check browser console for duplicate 'Initializing Live Updates' messages")
    print("   - Look for multiple WebSocket connection attempts")
    print()
    print("2. **Page refresh/navigation issues:**")
    print("   - WebSocket tries to reconnect when page is navigating away")
    print("   - Check if connections happen during page transitions")
    print()
    print("3. **Session/cookie issues:**")
    print("   - Authentication fails intermittently")
    print("   - Check browser Application tab → Cookies for sessionid")
    print()
    print("4. **Browser console errors:**")
    print("   - JavaScript errors interrupting WebSocket handling")
    print("   - Check for any red error messages in console")

def main():
    print("🔍 WEBSOCKET CONNECTION LOOP DEBUGGER")
    print("=" * 60)
    
    # Run checks
    check_session_middleware()
    print()
    check_channels_auth()
    print()
    simulate_connection_test()
    print()
    check_browser_issues()
    print()
    monitor_django_logs()
    
    print("\n💡 DEBUGGING STEPS")
    print("=" * 60)
    print("1. **Check Django server console** for WebSocket connection messages")
    print("2. **Open browser console** (F12) and look for:")
    print("   - Multiple 'Initializing Live Updates' messages")
    print("   - Rapid WebSocket connect/disconnect cycles")
    print("   - JavaScript errors")
    print("3. **Check Network tab** in browser dev tools:")
    print("   - Look for multiple WebSocket connection attempts")
    print("   - Check connection close codes")
    print("4. **Try in incognito mode** to rule out cache/extension issues")
    print()
    print("🎯 MOST LIKELY CAUSES:")
    print("- Multiple WebSocket connections from different templates")
    print("- JavaScript errors causing reconnection loops")
    print("- Session authentication issues")
    print("- Page navigation triggering reconnections")

if __name__ == "__main__":
    main()
