import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import LocationQueueConfig, QueuedJob
from django.utils import timezone
from datetime import timedelta

class QueueUpdatesConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()
        await self.channel_layer.group_add("queue_updates", self.channel_name)
        await self.send_initial_data()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard("queue_updates", self.channel_name)

    async def queue_update(self, event):
        await self.send(text_data=json.dumps(event['data']))

    @database_sync_to_async
    def get_queue_data(self):
        locations = LocationQueueConfig.objects.select_related('location').all()
        
        overview = {
            'total_active_jobs': QueuedJob.objects.filter(status='processing').count(),
            'total_waiting_jobs': QueuedJob.objects.filter(status='queued').count(),
            'total_active_workers': sum(loc.active_workers for loc in locations),
            'total_max_workers': sum(loc.max_workers for loc in locations),
            'locations': []
        }
        
        for loc in locations:
            jobs = QueuedJob.objects.filter(location=loc.location)
            completed = jobs.filter(status='completed').count()
            failed = jobs.filter(status='failed').count()
            total = completed + failed
            
            overview['locations'].append({
                'id': str(loc.location.id),
                'active_workers': loc.active_workers,
                'max_workers': loc.max_workers,
                'queued': jobs.filter(status='queued').count(),
                'processing': jobs.filter(status='processing').count(),
                'success_rate': round((completed / total) * 100, 1) if total > 0 else 100,
            })
        
        return overview

    async def send_initial_data(self):
        data = await self.get_queue_data()
        await self.send(text_data=json.dumps({
            'type': 'initial_data',
            'data': data
        }))