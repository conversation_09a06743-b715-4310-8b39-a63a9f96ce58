@echo off
echo 🚀 STARTING LIVE SYNC SYSTEM
echo ==============================
echo.
echo Starting components for live database sync every 5 seconds:
echo   1. Celery Beat (task scheduler)
echo   2. Scheduler Worker (processes sync tasks)
echo   3. Django Server (already running)
echo.
echo Press Ctrl+C to stop all components
echo.

REM Start Celery Beat in background
echo 📅 Starting Celery Beat...
start "Celery Beat" cmd /k "celery -A config beat --loglevel=info"

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Start Scheduler Worker in background
echo 👷 Starting Scheduler Worker...
start "Scheduler Worker" cmd /k "celery -A config worker --loglevel=info --concurrency=1 --queues=scheduler --hostname=scheduler_worker@%%h --pool=solo"


REM Wait a moment
timeout /t 2 /nobreak >nul
REM Start Project
echo 👷 Starting Scheduler Worker...
start "Project" cmd /k "python manage.py runserver"

echo.
echo ✅ LIVE SYNC COMPONENTS STARTED
echo ================================
echo.
echo 📊 Status:
echo   • Celery Beat: Running (schedules sync every 5 seconds)
echo   • Scheduler Worker: Running (executes sync tasks)
echo   • Django Server: Keep running in your main terminal
echo.
echo 🔍 Monitoring:
echo   • Check admin UI: http://localhost:8000/queue/admin/queue-overview/
echo   • Database will sync automatically every 5 seconds
echo   • UI will update every 5 seconds
echo.
echo 🛑 To stop: Close the Celery Beat and Scheduler Worker windows
echo.
pause
