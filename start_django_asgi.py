#!/usr/bin/env python
"""
Direct ASGI server startup script for Django with WebSocket support
"""
import os
import sys
import django
from django.core.asgi import get_asgi_application

def start_asgi_server():
    """Start Django ASGI server directly"""
    print("🚀 Starting Django ASGI Server with WebSocket Support")
    print("=" * 60)
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    
    # Setup Django
    django.setup()
    
    # Verify configuration
    from django.conf import settings
    print(f"✅ ASGI Application: {getattr(settings, 'ASGI_APPLICATION', 'Not configured')}")
    print(f"✅ Channel Layers: {getattr(settings, 'CHANNEL_LAYERS', {}).get('default', {}).get('BACKEND', 'Not configured')}")
    print(f"✅ Debug Mode: {settings.DEBUG}")
    
    # Test WebSocket infrastructure
    try:
        from channels.layers import get_channel_layer
        channel_layer = get_channel_layer()
        if channel_layer:
            print(f"✅ Channel Layer: {type(channel_layer).__name__}")
        else:
            print("❌ Channel Layer: Not configured")
    except Exception as e:
        print(f"❌ Channel Layer Error: {e}")
    
    # Test consumer imports
    try:
        from queue_system.consumer import AdminLiveUpdatesConsumer
        print("✅ AdminLiveUpdatesConsumer: Imported successfully")
    except Exception as e:
        print(f"❌ Consumer Import Error: {e}")
    
    print("\n🌐 Server will be available at:")
    print("   HTTP: http://localhost:8000/admin/")
    print("   WebSocket: ws://localhost:8000/ws/admin/live/")
    print("\n📋 To test WebSocket:")
    print("   1. Open test_websocket.html in your browser")
    print("   2. Or check browser console in admin interface")
    print("\n" + "=" * 60)
    
    # Start the server using uvicorn (if available) or daphne
    try:
        import uvicorn
        print("🚀 Starting with uvicorn...")
        uvicorn.run(
            "config.asgi:application",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
    except ImportError:
        try:
            import daphne
            print("🚀 Starting with daphne...")
            from daphne.management.commands.runserver import Command as DaphneCommand
            command = DaphneCommand()
            command.run(
                application=get_asgi_application(),
                bind="127.0.0.1",
                port=8000,
                verbosity=2
            )
        except ImportError:
            print("❌ Neither uvicorn nor daphne is installed.")
            print("📦 Install one of them:")
            print("   pip install uvicorn")
            print("   pip install daphne")
            print("\n🔄 Falling back to Django runserver...")
            
            # Fallback to Django runserver
            from django.core.management import execute_from_command_line
            sys.argv = ['manage.py', 'runserver', '8000']
            execute_from_command_line(sys.argv)

if __name__ == "__main__":
    start_asgi_server()
