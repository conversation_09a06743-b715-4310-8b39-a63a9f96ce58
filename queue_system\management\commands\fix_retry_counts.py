from django.core.management.base import BaseCommand
from django.db import models
from queue_system.models import QueuedJob

class Command(BaseCommand):
    help = 'Fix jobs with retry_count > max_retries'

    def handle(self, *args, **options):
        # Find jobs with invalid retry counts
        invalid_jobs = QueuedJob.objects.filter(retry_count__gt=models.F('max_retries'))
        
        self.stdout.write(f"Found {invalid_jobs.count()} jobs with invalid retry counts")
        
        for job in invalid_jobs:
            old_retry_count = job.retry_count
            # Cap retry count at max_retries
            job.retry_count = job.max_retries
            job.save()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"Fixed job {job.id}: {old_retry_count}/{job.max_retries} -> {job.retry_count}/{job.max_retries}"
                )
            )
        
        self.stdout.write(
            self.style.SUCCESS(f"Successfully fixed {invalid_jobs.count()} jobs")
        )
