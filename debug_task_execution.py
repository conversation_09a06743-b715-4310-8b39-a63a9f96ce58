#!/usr/bin/env python3
"""
Debug task execution to see what's happening
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.models import QueuedJob, JobError
from queue_system.tasks import process_order
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_task_execution():
    """Debug the task execution"""
    
    # Get job 94
    job = QueuedJob.objects.get(id=94)
    
    print(f"🔍 Debugging Job #{job.id}")
    print(f"Order ID: {job.order.id}")
    print(f"Location ID: {job.location.id}")
    print(f"Current Status: {job.status}")
    print(f"Current Error: {job.error_message}")
    print(f"Current Failure Reason: {job.failure_reason}")
    
    # Reset job to queued status
    job.status = 'queued'
    job.started_at = None
    job.worker_id = None
    job.save()
    
    print(f"\n🔄 Reset job to queued status")
    
    # Clear existing JobError records for this test
    existing_errors = JobError.objects.filter(job=job)
    print(f"📋 Found {existing_errors.count()} existing JobError records")
    
    # Manually call the task
    print(f"\n🚀 Manually executing process_order task...")
    
    try:
        # Create a mock task instance
        class MockTask:
            def __init__(self):
                self.request = type('obj', (object,), {'id': 'debug-task-123'})
        
        mock_task = MockTask()
        
        # Call the task function directly with correct signature
        result = process_order.run(str(job.order.id), str(job.location.id))
        
        print(f"✅ Task completed. Result: {result}")
        
    except Exception as e:
        print(f"❌ Task failed with exception: {e}")
        import traceback
        print(traceback.format_exc())
    
    # Check job status after execution
    job.refresh_from_db()
    print(f"\n📊 Job Status After Execution:")
    print(f"Status: {job.status}")
    print(f"Error Message: {job.error_message}")
    print(f"Failure Reason: {job.failure_reason}")
    print(f"Retry Count: {job.retry_count}")
    
    # Check JobError records
    errors = JobError.objects.filter(job=job).order_by('-occurred_at')
    print(f"\n📋 JobError Records: {errors.count()}")
    
    for i, error in enumerate(errors[:3]):
        print(f"Error {i+1}:")
        print(f"  Message: {error.error_message}")
        print(f"  Occurred: {error.occurred_at}")
        print(f"  Details: {error.error_details}")
        print()

if __name__ == "__main__":
    print("🔧 Debug Task Execution")
    print("=" * 50)
    
    debug_task_execution()
    
    print(f"\n🎉 Debug Complete!")
