#!/usr/bin/env python
"""
Test real-time updates for queue admin page
"""
import os
import sys
import django
import time
import asyncio

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def test_realtime_updates():
    """Test real-time updates by changing job statuses"""
    setup_django()
    
    print("🎯 TESTING REAL-TIME QUEUE UPDATES")
    print("=" * 60)
    
    try:
        from queue_system.models import QueuedJob
        from django.utils import timezone
        
        # Get existing jobs
        jobs = list(QueuedJob.objects.all()[:3])  # Test with first 3 jobs
        
        if not jobs:
            print("❌ No jobs found to test with")
            print("💡 Create some test jobs first")
            return False
            
        print(f"📊 Found {len(jobs)} jobs to test with")
        
        # Show initial state
        print("\n📋 INITIAL STATE:")
        for job in jobs:
            print(f"  Job {job.id}: {job.status}")
            
        print(f"\n🎬 STARTING REAL-TIME UPDATE TEST")
        print("👀 Watch the queue admin page for live updates!")
        print("📱 Expected: Numbers and table rows should update automatically")
        
        # Test sequence: change statuses in a cycle
        status_cycle = ['queued', 'processing', 'completed', 'failed', 'review']
        
        for round_num in range(3):  # 3 rounds of updates
            print(f"\n🔄 ROUND {round_num + 1}/3")
            print("-" * 40)
            
            for i, job in enumerate(jobs):
                # Pick next status in cycle
                current_index = status_cycle.index(job.status) if job.status in status_cycle else 0
                next_status = status_cycle[(current_index + 1) % len(status_cycle)]
                
                old_status = job.status
                job.status = next_status
                job.save()
                
                print(f"✅ Job {job.id}: {old_status} → {next_status}")
                print(f"   📡 WebSocket update should be sent automatically")
                print(f"   👀 Check admin page for live update")
                
                # Wait between updates to see the changes
                time.sleep(2)
                
        print(f"\n🎉 TEST COMPLETED!")
        print("=" * 60)
        
        # Show final state
        jobs = list(QueuedJob.objects.filter(id__in=[j.id for j in jobs]))
        print("📋 FINAL STATE:")
        for job in jobs:
            print(f"  Job {job.id}: {job.status}")
            
        print(f"\n📊 EXPECTED RESULTS:")
        print("✅ Queue statistics should have updated in real-time")
        print("✅ Job table rows should have changed status")
        print("✅ Visual feedback (highlighting) should have appeared")
        print("✅ No page refresh needed")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_current_stats():
    """Show current queue statistics"""
    setup_django()
    
    print("\n📊 CURRENT QUEUE STATISTICS")
    print("=" * 60)
    
    try:
        from queue_system.models import QueuedJob
        
        total = QueuedJob.objects.count()
        queued = QueuedJob.objects.filter(status='queued').count()
        processing = QueuedJob.objects.filter(status='processing').count()
        completed = QueuedJob.objects.filter(status='completed').count()
        failed = QueuedJob.objects.filter(status='failed').count()
        review = QueuedJob.objects.filter(status='review').count()
        
        print(f"📈 Total: {total}")
        print(f"⏳ Queued: {queued}")
        print(f"🔄 Processing: {processing}")
        print(f"✅ Completed: {completed}")
        print(f"❌ Failed: {failed}")
        print(f"🔍 Review: {review}")
        
        print(f"\n💡 These numbers should match the admin page")
        print(f"🔄 After running the test, these should update live")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to get stats: {e}")
        return False

def main():
    print("🚀 REAL-TIME QUEUE UPDATES TEST")
    print("=" * 70)
    
    print("📋 INSTRUCTIONS:")
    print("1. Make sure Django server is running")
    print("2. Open queue admin page: http://localhost:8000/admin/queue_system/queuedjob/")
    print("3. Open browser console (F12) to see debug messages")
    print("4. Position windows so you can see both this terminal and the admin page")
    print("5. Run this test and watch for live updates")
    
    # Show current stats
    stats_ok = show_current_stats()
    
    if stats_ok:
        print(f"\n⏰ STARTING TEST IN 5 SECONDS...")
        print("👀 Get ready to watch the admin page!")
        
        for i in range(5, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
            
        # Run the test
        test_ok = test_realtime_updates()
        
        # Show final stats
        if test_ok:
            show_current_stats()
    else:
        test_ok = False
    
    print("\n📊 TEST SUMMARY")
    print("=" * 70)
    print(f"Current Stats: {'✅ PASS' if stats_ok else '❌ FAIL'}")
    print(f"Real-time Test: {'✅ PASS' if test_ok else '❌ FAIL'}")
    
    if test_ok:
        print("\n🎉 Test completed successfully!")
        print("\n📋 What you should have seen:")
        print("  ✅ Queue statistics numbers changing in real-time")
        print("  ✅ Job table rows updating status automatically")
        print("  ✅ Visual feedback (highlighting/animations)")
        print("  ✅ Browser console showing WebSocket messages")
        
        print("\n🔍 If you didn't see live updates:")
        print("  1. Check browser console for WebSocket messages")
        print("  2. Verify Django console shows signal debug messages")
        print("  3. Check if global WebSocket is connected")
        print("  4. Try refreshing the admin page")
    else:
        print("\n⚠️ Test failed - check the errors above")
        
    print("\n💡 Next: Check browser console for these debug messages:")
    print("  📨 Global WebSocket received message")
    print("  📊 Queue page processing WebSocket data: admin_update")
    print("  🎯 updateDashboard called with data")
    print("  🔄 updateStatCard: queued = X")
    print("  ✅ Updated queued card to X")

if __name__ == "__main__":
    main()
