#!/usr/bin/env python3
"""
Test script to demonstrate detailed error descriptions in the queue system.
This script simulates various bot errors to show how they are parsed and displayed.
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.tasks import parse_bot_error
from queue_system.models import QueuedJob, JobError
from orders.models import order as Order
from locations.models import Location
from django.utils import timezone

def test_error_parsing():
    """Test the parse_bot_error function with various error scenarios"""
    
    print("🧪 Testing Error Parsing Function")
    print("=" * 50)
    
    # Test cases with different error types
    test_cases = [
        {
            "name": "Gender Dropdown Error",
            "error_message": "Error clicking element: Unable to select gender from the drop down menu",
            "error_trace": "Traceback (most recent call last):\n  File 'barbados_form_1.py', line 45, in fill_form\n    select_gender(driver, 'Male')\n  File 'barbados_form_1.py', line 78, in select_gender\n    raise Exception('Unable to select gender from the drop down menu')\nException: Unable to select gender from the drop down menu"
        },
        {
            "name": "Page Timeout Error",
            "error_message": "TimeoutException: Page did not load within 30 seconds",
            "error_trace": "Traceback (most recent call last):\n  File 'barbados_form_1.py', line 25, in navigate_to_form\n    WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.ID, 'form-container')))\nselenium.common.exceptions.TimeoutException: Message: \nStacktrace:"
        },
        {
            "name": "Element Not Found Error",
            "error_message": "NoSuchElementException: Unable to locate element: {'id': 'first_name_field'}",
            "error_trace": "Traceback (most recent call last):\n  File 'barbados_form_1.py', line 55, in fill_personal_info\n    first_name_field = driver.find_element(By.ID, 'first_name_field')\nselenium.common.exceptions.NoSuchElementException: Message: Unable to locate element: {'id': 'first_name_field'}"
        },
        {
            "name": "Bot Import Error",
            "error_message": "ImportError: Failed to import Barbados bot module",
            "error_trace": "Traceback (most recent call last):\n  File 'tasks.py', line 120, in process_order\n    from bots.barbados_form_1 import run_barbados_bot\nImportError: No module named 'bots.barbados_form_1'"
        },
        {
            "name": "WebDriver Error",
            "error_message": "WebDriverException: Chrome browser failed to start",
            "error_trace": "Traceback (most recent call last):\n  File 'barbados_form_1.py', line 15, in setup_driver\n    driver = webdriver.Chrome(options=chrome_options)\nselenium.common.exceptions.WebDriverException: Message: unknown error: Chrome failed to start"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 30)
        
        error_msg, detailed_description, failure_reason = parse_bot_error(
            test_case['error_message'], 
            test_case['error_trace']
        )
        
        print(f"📝 Error Message: {error_msg}")
        print(f"🔍 Failure Reason: {failure_reason}")
        print(f"📋 Detailed Description:")
        print(detailed_description)
        print()

def create_test_job_with_error():
    """Create a test job with detailed error information"""
    
    print("\n🏗️  Creating Test Job with Detailed Error")
    print("=" * 50)
    
    try:
        # Get or create a test location
        location, created = Location.objects.get_or_create(
            location_name="Test-Barbados",
            defaults={
                'description': 'Test location for demonstrating detailed error descriptions',
                'traveller_price': 100.00,
                'cost_price': 50.00
            }
        )
        
        # Create a unique test order
        import uuid
        unique_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
        order = Order.objects.create(
            customer_email=unique_email,
            first_name='Test',
            surname='User',
            location=location,
            status='pending'
        )
        
        # Create a test job
        job = QueuedJob.objects.create(
            order=order,
            location=location,
            status='failed',
            retry_count=2,
            max_retries=3,
            failure_reason='gender_dropdown_error',
            error_message='Bot execution failed'
        )
        
        # Simulate the detailed error parsing
        error_message = "Error clicking element: Unable to select gender from the drop down menu"
        error_trace = """Traceback (most recent call last):
  File 'barbados_form_1.py', line 45, in fill_form
    select_gender(driver, 'Male')
  File 'barbados_form_1.py', line 78, in select_gender
    raise Exception('Unable to select gender from the drop down menu')
Exception: Unable to select gender from the drop down menu"""
        
        error_msg, detailed_description, failure_reason = parse_bot_error(error_message, error_trace)
        
        # Create detailed error record
        error_details = {
            'error_type': 'Exception',
            'error_message': error_message,
            'detailed_description': detailed_description,
            'order_id': str(order.id),
            'location': location.location_name,
            'timestamp': timezone.now().isoformat(),
            'bot_module': 'Barbados_form_1',
            'context': 'Bot execution failed during form processing'
        }
        
        JobError.objects.create(
            job=job,
            error_message=detailed_description,
            error_trace=error_trace,
            error_details=error_details
        )
        
        # Update job with detailed failure reason
        job.failure_reason = failure_reason
        job.save()
        
        print(f"✅ Created test job #{job.id}")
        print(f"📧 Customer: {order.first_name} {order.surname} ({order.customer_email})")
        print(f"📍 Location: {location.location_name}")
        print(f"❌ Failure Reason: {failure_reason}")
        print(f"📋 Detailed Description:")
        print(detailed_description)
        print(f"\n🔗 View in admin: http://localhost:8000/queue/admin/job/{job.id}/")
        
        return job
        
    except Exception as e:
        print(f"❌ Error creating test job: {str(e)}")
        return None

if __name__ == "__main__":
    print("🚀 Testing Detailed Error Descriptions")
    print("=" * 60)
    
    # Test the error parsing function
    test_error_parsing()
    
    # Create a test job with detailed error
    test_job = create_test_job_with_error()
    
    if test_job:
        print(f"\n✨ Test completed! Check the admin interface to see the detailed error descriptions.")
        print(f"🔗 Job Details: http://localhost:8000/queue/admin/job/{test_job.id}/")
        print(f"🔗 Admin Jobs List: http://localhost:8000/admin/queue_system/queuedjob/")
