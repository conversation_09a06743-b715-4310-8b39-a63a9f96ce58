#!/usr/bin/env python
"""
Debug script to test queue statistics live updates
"""
import os
import sys
import django
import asyncio
import time

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

async def test_websocket_updates():
    """Test WebSocket updates for queue statistics"""
    setup_django()
    
    print("🧪 TESTING QUEUE STATISTICS LIVE UPDATES")
    print("=" * 60)
    
    try:
        from channels.layers import get_channel_layer
        from queue_system.consumer import AdminLiveUpdatesConsumer
        
        channel_layer = get_channel_layer()
        if not channel_layer:
            print("❌ No channel layer configured")
            return
            
        print(f"✅ Channel layer: {type(channel_layer).__name__}")
        
        # Create consumer instance to get data
        consumer = AdminLiveUpdatesConsumer()
        
        print("\n🔍 TESTING DATA GENERATION")
        print("-" * 40)
        
        # Get current admin data
        admin_data = await consumer.get_admin_data()
        print("📊 Current admin data structure:")
        print(f"  Keys: {list(admin_data.keys())}")
        
        if 'overview' in admin_data:
            overview = admin_data['overview']
            print(f"  Overview: {overview}")
        else:
            print("  ❌ No 'overview' key in admin data")
            
        print("\n🚀 SENDING TEST UPDATE")
        print("-" * 40)
        
        # Send test update to admin group
        test_message = {
            'type': 'admin_update',
            'data': admin_data
        }
        
        await channel_layer.group_send('admin_live_updates', {
            'type': 'send_update',
            'message': test_message
        })
        
        print("✅ Test update sent to admin_live_updates group")
        print("📋 Message structure:")
        print(f"  Type: {test_message['type']}")
        print(f"  Data keys: {list(test_message['data'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_updates():
    """Test manual queue updates"""
    setup_django()
    
    print("\n🔧 TESTING MANUAL QUEUE UPDATES")
    print("=" * 60)
    
    try:
        from queue_system.models import QueuedJob
        from django.utils import timezone
        
        # Get current counts
        total_jobs = QueuedJob.objects.count()
        queued = QueuedJob.objects.filter(status='queued').count()
        processing = QueuedJob.objects.filter(status='processing').count()
        completed = QueuedJob.objects.filter(status='completed').count()
        failed = QueuedJob.objects.filter(status='failed').count()
        review = QueuedJob.objects.filter(status='review').count()
        
        print("📊 Current Queue Statistics:")
        print(f"  Total: {total_jobs}")
        print(f"  Queued: {queued}")
        print(f"  Processing: {processing}")
        print(f"  Completed: {completed}")
        print(f"  Failed: {failed}")
        print(f"  Review: {review}")
        
        # Create a test job to trigger updates
        if total_jobs == 0:
            print("\n🆕 Creating test job...")
            from orders.models import Order, Location
            
            # Get or create test location
            location, created = Location.objects.get_or_create(
                location_name='Test Location',
                defaults={'location_code': 'TEST'}
            )
            
            # Get or create test order
            order, created = Order.objects.get_or_create(
                first_name='Test',
                surname='User',
                defaults={
                    'email': '<EMAIL>',
                    'phone': '1234567890',
                    'date_of_birth': '1990-01-01',
                    'location': location
                }
            )
            
            # Create test job
            job = QueuedJob.objects.create(
                order=order,
                location=location,
                status='queued',
                created_at=timezone.now()
            )
            
            print(f"✅ Created test job: {job.id}")
            return True
        else:
            print("✅ Jobs exist - ready for testing")
            return True
            
    except Exception as e:
        print(f"❌ Manual test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🎯 QUEUE STATISTICS DEBUG TOOL")
    print("=" * 70)
    
    print("📋 Instructions:")
    print("1. Open queue admin page: http://localhost:8000/admin/queue_system/queuedjob/")
    print("2. Open browser console (F12)")
    print("3. Run this script to send test updates")
    print("4. Watch console for debug messages")
    print("5. Check if queue statistics update")
    
    # Test manual updates first
    manual_ok = test_manual_updates()
    
    if manual_ok:
        # Test WebSocket updates
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        websocket_ok = loop.run_until_complete(test_websocket_updates())
        loop.close()
    else:
        websocket_ok = False
    
    print("\n📊 DEBUG SUMMARY")
    print("=" * 60)
    print(f"Manual Updates: {'✅ PASS' if manual_ok else '❌ FAIL'}")
    print(f"WebSocket Updates: {'✅ PASS' if websocket_ok else '❌ FAIL'}")
    
    if manual_ok and websocket_ok:
        print("\n🎉 All tests passed!")
        print("\n📋 What to check in browser console:")
        print("  📨 Global WebSocket received message: ...")
        print("  📊 Queue page processing WebSocket data: admin_update")
        print("  🎯 updateDashboard called with data: ...")
        print("  🔄 updateStatCard: queued = X")
        print("  ✅ Updated queued card to X")
        
        print("\n🔍 If you don't see these messages:")
        print("  1. Check if global WebSocket is connected")
        print("  2. Verify queue page message listener is set up")
        print("  3. Check for JavaScript errors")
        print("  4. Try refreshing the page")
    else:
        print("\n⚠️ Some tests failed - check the errors above")

if __name__ == "__main__":
    main()
