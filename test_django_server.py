#!/usr/bin/env python
"""
Test script to start Django server with ASGI support for WebSockets
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

def start_django_server():
    """Start Django server with ASGI support"""
    print("🚀 Starting Django server with ASGI support...")
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    
    # Setup Django
    django.setup()
    
    # Check if ASGI is configured
    from django.conf import settings
    print(f"ASGI Application: {getattr(settings, 'ASGI_APPLICATION', 'Not configured')}")
    print(f"Channel Layers: {getattr(settings, 'CHANNEL_LAYERS', 'Not configured')}")
    
    # Start the server
    print("Starting Django development server...")
    sys.argv = ['manage.py', 'runserver', '8000']
    execute_from_command_line(sys.argv)

if __name__ == "__main__":
    start_django_server()
