from django import template
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>

register = template.Library()

@register.filter
def get_range(value):
    """
    Returns a range of numbers from 0 to value
    """
    return range(int(value))

@register.filter
def mul(value, arg):
    """
    Multiplies the value by the argument
    """
    return float(value) * float(arg)

@register.filter
def div(value, arg):
    """
    Divides the value by the argument
    """
    if float(arg) == 0:
        return 0
    return float(value) / float(arg)

@register.filter
def sub(value, arg):
    """
    Subtracts the argument from the value
    """
    return int(value) - int(arg)
