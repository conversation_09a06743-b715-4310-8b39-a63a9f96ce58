{% extends "admin/change_list.html" %}
{% load static %}
{% load mathfilters %}
{% load queue_system_tags %}

{% block extrahead %}
{{ block.super }}
<style>
  .dashboard-container {
    margin-bottom: 30px;
  }
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
  }
  .stat-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 20px;
    text-align: center;
    transition: transform 0.2s;
  }
  .stat-card:hover {
    transform: translateY(-5px);
  }
  .stat-value {
    font-size: 32px;
    font-weight: bold;
    margin: 10px 0;
  }
  .stat-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  .queued { color: #ff9800; }
  .processing { color: #2196f3; }
  .completed { color: #4caf50; }
  .failed { color: #f44336; }
  .priority { color: #9c27b0; }
  
  .location-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  }
  .location-table th {
    background: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    color: #333;
  }
  .location-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
  }
  .location-table tr:last-child td {
    border-bottom: none;
  }
  .location-table tr:hover {
    background: #f9f9f9;
  }
  .badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: white;
  }
  .badge-blue { background: #2196f3; }
  .badge-green { background: #4caf50; }
  .badge-orange { background: #ff9800; }
  .badge-red { background: #f44336; }
  
  .worker-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
  }
  .worker-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ddd;
  }
  .worker-dot.active {
    background: #4caf50;
  }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-label">Queued</div>
      <div class="stat-value queued">{{ queued_count }}</div>
    </div>
    <div class="stat-card">
      <div class="stat-label">Processing</div>
      <div class="stat-value processing">{{ processing_count }}</div>
    </div>
    <div class="stat-card">
      <div class="stat-label">Completed</div>
      <div class="stat-value completed">{{ completed_count }}</div>
    </div>
    <div class="stat-card">
      <div class="stat-label">Failed</div>
      <div class="stat-value failed">{{ failed_count }}</div>
    </div>
    <div class="stat-card">
      <div class="stat-label">Priority</div>
      <div class="stat-value priority">{{ priority_count }}</div>
    </div>
  </div>
  
  <h2>Location Queue Status</h2>
  <table class="location-table">
    <thead>
      <tr>
        <th>Location</th>
        <th>Queued</th>
        <th>Processing</th>
        <th>Completed</th>
        <th>Failed</th>
        <th>Workers</th>
      </tr>
    </thead>
    <tbody>
      {% for location in location_stats %}
      <tr>
        <td>{{ location.name }}</td>
        <td>
          {% if location.queued > 0 %}
          <span class="badge badge-orange">{{ location.queued }}</span>
          {% else %}
          <span>{{ location.queued }}</span>
          {% endif %}
        </td>
        <td>
          {% if location.processing > 0 %}
          <span class="badge badge-blue">{{ location.processing }}</span>
          {% else %}
          <span>{{ location.processing }}</span>
          {% endif %}
        </td>
        <td>{{ location.completed }}</td>
        <td>
          {% if location.failed > 0 %}
          <span class="badge badge-red">{{ location.failed }}</span>
          {% else %}
          <span>{{ location.failed }}</span>
          {% endif %}
        </td>
        <td>
          <div class="worker-indicator">
            {% for i in location.active_workers|get_range %}
              <span class="worker-dot active" title="Active worker"></span>
            {% endfor %}
            {% with inactive=location.max_workers|sub:location.active_workers|default:0 %}
              {% for i in inactive|get_range %}
                <span class="worker-dot" title="Inactive worker"></span>
              {% endfor %}
            {% endwith %}
            <span>{{ location.active_workers }}/{{ location.max_workers }}</span>
          </div>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

{{ block.super }}
{% endblock %}





