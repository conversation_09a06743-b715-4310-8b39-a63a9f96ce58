#!/usr/bin/env python3
"""
Check if Celery workers are running and queue status
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from config.celery import app as celery_app
from queue_system.models import QueuedJob, Location

def check_celery_workers():
    """Check if Celery workers are running"""
    
    print("🔍 Checking Celery Workers")
    print("=" * 40)
    
    try:
        # Get active workers
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            print(f"✅ Found {len(active_workers)} active workers:")
            for worker_name, tasks in active_workers.items():
                print(f"   - {worker_name}: {len(tasks)} active tasks")
        else:
            print("❌ No active workers found")
            
        # Check registered workers
        registered = inspect.registered()
        if registered:
            print(f"\n📋 Registered workers:")
            for worker_name, tasks in registered.items():
                print(f"   - {worker_name}: {len(tasks)} registered tasks")
        
        return len(active_workers) > 0 if active_workers else False
        
    except Exception as e:
        print(f"❌ Error checking workers: {str(e)}")
        return False

def check_queue_status():
    """Check current queue status"""
    
    print("\n📊 Queue Status")
    print("=" * 40)
    
    # Count jobs by status
    statuses = ['queued', 'processing', 'completed', 'failed', 'review']
    
    for status in statuses:
        count = QueuedJob.objects.filter(status=status).count()
        print(f"   {status.capitalize()}: {count}")
    
    # Show recent jobs
    recent_jobs = QueuedJob.objects.order_by('-created_at')[:5]
    
    if recent_jobs:
        print(f"\n📋 Recent Jobs:")
        for job in recent_jobs:
            print(f"   #{job.id}: {job.order.first_name} {job.order.surname} - {job.status}")

def start_worker_if_needed():
    """Provide instructions to start worker if none are running"""
    
    print("\n🚀 Starting Worker")
    print("=" * 40)
    
    locations = Location.objects.all()
    
    if locations:
        location = locations.first()
        queue_name = f'location.{location.id}'
        
        print(f"💡 To start a worker for testing, run:")
        print(f"   celery -A config worker --loglevel=info --queues={queue_name}")
        print(f"")
        print(f"🔄 Or start workers for all locations:")
        for loc in locations:
            queue_name = f'location.{loc.id}'
            print(f"   celery -A config worker --loglevel=info --queues={queue_name} --hostname=worker-{loc.location_name}@%h")
    else:
        print("❌ No locations found")

if __name__ == "__main__":
    print("🔧 Celery Worker Status Check")
    print("=" * 50)
    
    # Check if workers are running
    workers_running = check_celery_workers()
    
    # Check queue status
    check_queue_status()
    
    # Provide instructions if no workers
    if not workers_running:
        start_worker_if_needed()
    else:
        print(f"\n✅ Workers are running - ready to process jobs!")
        print(f"💡 You can now run: python create_test_job.py")
