#!/usr/bin/env python
"""
Test periodic updates by changing job status and monitoring console
"""
import os
import sys
import django
import time

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def test_periodic_updates():
    """Test periodic updates by changing job status"""
    setup_django()
    
    print("🔄 TESTING PERIODIC UPDATES")
    print("=" * 60)
    
    try:
        from queue_system.models import QueuedJob
        
        # Get a job to modify
        job = QueuedJob.objects.first()
        if not job:
            print("❌ No jobs found to test with")
            return False
            
        print(f"📝 Found job {job.id} with status: {job.status}")
        print(f"📋 Instructions:")
        print(f"  1. Open queue admin page: http://localhost:8000/admin/queue_system/queuedjob/")
        print(f"  2. Open browser console (F12)")
        print(f"  3. Watch Django server console for periodic update messages")
        print(f"  4. This script will change job status every 10 seconds")
        
        # Status cycle
        statuses = ['queued', 'processing', 'completed', 'failed', 'review']
        current_index = 0
        
        for round_num in range(5):  # 5 rounds of changes
            print(f"\n🔄 ROUND {round_num + 1}/5")
            print("-" * 40)
            
            # Get next status
            new_status = statuses[current_index % len(statuses)]
            old_status = job.status
            
            print(f"🔄 Changing job {job.id}: {old_status} → {new_status}")
            job.status = new_status
            job.save()
            
            print(f"✅ Job status changed")
            print(f"📡 Signal should trigger WebSocket update")
            print(f"🔍 Periodic check should detect change within 2 seconds")
            print(f"👀 Watch Django console for:")
            print(f"   - '📊 Detected changes: ...'")
            print(f"   - '📤 Sending update to WebSocket client...'")
            print(f"   - '✅ Update sent via periodic check'")
            
            print(f"⏰ Waiting 10 seconds before next change...")
            time.sleep(10)
            
            current_index += 1
            
        print(f"\n🎉 TEST COMPLETED!")
        print("=" * 60)
        
        print(f"📊 Expected Django Console Messages:")
        print(f"  🔄 Starting periodic update check task")
        print(f"  🔍 Checking for data changes...")
        print(f"  📊 Detected changes: queued: 0 → 1")
        print(f"  📤 Sending update to WebSocket client...")
        print(f"  ✅ Update sent via periodic check")
        
        print(f"\n📊 Expected Browser Console Messages:")
        print(f"  📨 Received: admin_update")
        print(f"  📊 Queue page processing WebSocket data: admin_update")
        print(f"  🔄 updateStatCard: queued = 1")
        print(f"  ✅ Updated queued card to 1")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🎯 PERIODIC UPDATES TEST")
    print("=" * 70)
    
    print("📋 This test will:")
    print("  1. Change job statuses every 10 seconds")
    print("  2. Monitor Django console for periodic update messages")
    print("  3. Check if browser receives live updates")
    
    print(f"\n⚠️ IMPORTANT:")
    print("  1. Make sure Django server is running")
    print("  2. Open queue admin page in browser")
    print("  3. Open browser console (F12)")
    print("  4. Position windows to see both Django console and browser")
    
    input("\n⏰ Press Enter when ready to start the test...")
    
    success = test_periodic_updates()
    
    if success:
        print(f"\n🎉 Test completed!")
        print(f"📋 What you should have seen:")
        print(f"  ✅ Django console showing periodic update messages")
        print(f"  ✅ Browser console showing admin_update messages")
        print(f"  ✅ Queue statistics updating automatically")
        print(f"  ✅ Job table rows changing status")
    else:
        print(f"\n❌ Test failed - check the errors above")
        
    print(f"\n🔧 If periodic updates aren't working:")
    print(f"  1. Check Django console for error messages")
    print(f"  2. Verify WebSocket connection in browser")
    print(f"  3. Check if periodic task is starting")
    print(f"  4. Look for asyncio or task-related errors")

if __name__ == "__main__":
    main()
