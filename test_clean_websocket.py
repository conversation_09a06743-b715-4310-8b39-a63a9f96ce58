#!/usr/bin/env python
"""
Test the clean WebSocket implementation
"""
import os
import sys
import django
import time

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def test_clean_implementation():
    """Test the clean WebSocket implementation"""
    print("🧹 TESTING CLEAN WEBSOCKET IMPLEMENTATION")
    print("=" * 60)
    
    print("✅ Clean implementation features:")
    print("  • Single global connection check")
    print("  • Simplified connection logic")
    print("  • Reduced reconnection attempts (max 2)")
    print("  • Longer reconnection delay (10 seconds)")
    print("  • Disabled page-specific WebSocket code")
    print("  • Removed duplicate functions")
    
    print("\n🔍 What to check in browser:")
    print("  1. Only ONE 'Starting Live Updates' message")
    print("  2. Only ONE WebSocket connection attempt")
    print("  3. Stable connection (no rapid reconnects)")
    print("  4. Clean console output")
    
    print("\n📋 Testing steps:")
    print("  1. Clear browser cache (Ctrl+F5)")
    print("  2. Open admin interface")
    print("  3. Check browser console")
    print("  4. Look for single connection message")
    print("  5. Verify stable indicator")
    
    print("\n🎯 Expected behavior:")
    print("  • 🟡 'Connecting...' (briefly)")
    print("  • 🟢 'Live Updates' (stable)")
    print("  • No reconnection loops")
    print("  • Clean console output")

def monitor_connections():
    """Monitor for connection patterns"""
    print("\n🔍 MONITORING GUIDE")
    print("=" * 60)
    
    print("✅ GOOD patterns in browser console:")
    print("  🚀 Starting Live Updates (Clean Version)")
    print("  🔗 Connecting to: ws://localhost:8000/ws/admin/live/")
    print("  ✅ WebSocket connected successfully")
    print("  📨 Received: initial_data")
    print("  [STABLE - no more messages]")
    
    print("\n❌ BAD patterns (connection loop):")
    print("  🚀 Starting Live Updates (Clean Version)")
    print("  🔗 Connecting to: ws://localhost:8000/ws/admin/live/")
    print("  ✅ WebSocket connected successfully")
    print("  🔌 WebSocket closed: 1006")
    print("  🔄 Will reconnect in 10s (attempt 1/2)")
    print("  🔗 Connecting to: ws://localhost:8000/ws/admin/live/")
    print("  [REPEATING CYCLE]")
    
    print("\n🎯 If you still see loops:")
    print("  1. Check Django server console for errors")
    print("  2. Try incognito mode")
    print("  3. Check for JavaScript errors")
    print("  4. Verify admin user authentication")

def check_django_logs():
    """Check what to look for in Django logs"""
    print("\n📊 DJANGO SERVER LOGS")
    print("=" * 60)
    
    print("✅ GOOD patterns in Django console:")
    print("  🔗 WebSocket connection attempt from user: your_username")
    print("  ✅ WebSocket accepted for admin user: your_username")
    print("  📊 Initial data sent to your_username")
    print("  [STABLE - connection stays open]")
    
    print("\n❌ BAD patterns (indicates issues):")
    print("  🔗 WebSocket connection attempt from user: your_username")
    print("  ✅ WebSocket accepted for admin user: your_username")
    print("  📊 Initial data sent to your_username")
    print("  🔌 WebSocket disconnected: user=your_username, code=1006")
    print("  🔗 WebSocket connection attempt from user: your_username")
    print("  [REPEATING CYCLE]")
    
    print("\n🔍 Common disconnect codes:")
    print("  • 1000: Normal close (expected when navigating away)")
    print("  • 1006: Abnormal close (connection lost)")
    print("  • 4003: Authentication failure")
    print("  • 4004: Data sending error")

def main():
    test_clean_implementation()
    monitor_connections()
    check_django_logs()
    
    print("\n🎯 FINAL CHECKLIST")
    print("=" * 60)
    print("□ Django server restarted")
    print("□ Browser cache cleared (Ctrl+F5)")
    print("□ Only one admin tab open")
    print("□ Logged in as admin user")
    print("□ Browser console shows single connection")
    print("□ Live indicator stable (not flickering)")
    print("□ Django console shows stable connection")
    print("□ No rapid reconnection cycles")
    
    print("\n🚀 If all checks pass, the connection loop is fixed!")
    print("🔧 If issues persist, the problem may be:")
    print("  • Browser extensions interfering")
    print("  • Network/proxy issues")
    print("  • Django session/authentication problems")
    print("  • Server-side errors in WebSocket consumer")

if __name__ == "__main__":
    main()
