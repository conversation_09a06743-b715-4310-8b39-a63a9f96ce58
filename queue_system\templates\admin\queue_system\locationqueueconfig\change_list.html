{% extends "admin/change_list.html" %}
{% load static %}
{% load mathfilters %}

{% block extrahead %}
{{ block.super }}
<style>
  .dashboard-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }
  .dashboard-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 20px;
    transition: transform 0.2s;
  }
  .dashboard-card:hover {
    transform: translateY(-5px);
  }
  .card-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  .card-value {
    font-size: 32px;
    font-weight: bold;
    color: #1976d2;
  }
  .card-subtitle {
    font-size: 13px;
    color: #999;
    margin-top: 5px;
  }
  .card-icon {
    float: right;
    font-size: 40px;
    color: rgba(25, 118, 210, 0.1);
  }
  .progress-container {
    margin-top: 15px;
    background: #f5f5f5;
    border-radius: 10px;
    height: 10px;
    overflow: hidden;
  }
  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #1976d2, #64b5f6);
    border-radius: 10px;
  }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
  <div class="dashboard-card">
    <div class="card-icon">📍</div>
    <div class="card-title">Total Locations</div>
    <div class="card-value">{{ total_locations }}</div>
    <div class="card-subtitle">Configured for queue processing</div>
  </div>
  
  <div class="dashboard-card">
    <div class="card-icon">⏱️</div>
    <div class="card-title">Time Window Locations</div>
    <div class="card-value">{{ time_window_locations }}</div>
    <div class="card-subtitle">Using scheduled processing</div>
  </div>
  
  <div class="dashboard-card">
    <div class="card-icon">🤖</div>
    <div class="card-title">Worker Utilization</div>
    <div class="card-value">{{ total_active_workers }} / {{ total_max_workers }}</div>
    <div class="card-subtitle">Active workers vs capacity</div>
    <div class="progress-container">
      <div class="progress-bar" style="width: {% if total_max_workers > 0 %}{{ total_active_workers|mul:100|div:total_max_workers }}{% else %}0{% endif %}%;"></div>
    </div>
  </div>
</div>

{{ block.super }}
{% endblock %}




