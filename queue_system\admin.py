from django.contrib import admin
from django.db import models
from django.db.models import Count
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from .models import LocationQueueConfig, <PERSON><PERSON><PERSON><PERSON>, JobError
from .admin_filters import <PERSON><PERSON>ueue<PERSON>ilter, FailureReasonFilter, RetryCountFilter

class JobErrorInline(admin.TabularInline):
    model = JobError
    extra = 0
    readonly_fields = ('occurred_at', 'error_message_formatted', 'error_trace')
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False

    def error_message_formatted(self, obj):
        """Display the detailed error description in a user-friendly format"""
        if obj.error_message:
            # Convert newlines to HTML breaks for better display
            formatted_message = obj.error_message.replace('\n', '<br>')
            return f'<div style="font-family: monospace; white-space: pre-wrap;">{formatted_message}</div>'
        return "No error message"
    error_message_formatted.short_description = 'Error Details'
    error_message_formatted.allow_tags = True

@admin.register(LocationQueueConfig)
class LocationQueueConfigAdmin(admin.ModelAdmin):
    list_display = ('location', 'has_time_window', 'window_days_before_travel', 
                    'max_workers', 'active_workers', 'priority_level', 'auto_scale', 'min_workers')
    list_filter = ('has_time_window', 'auto_scale')
    search_fields = ('location__location_name',)
    
    fieldsets = (
        (None, {
            'fields': ('location', 'priority_level')
        }),
        ('Time Window Settings', {
            'fields': ('has_time_window', 'window_days_before_travel')
        }),
        ('Worker Settings', {
            'fields': ('max_workers', 'active_workers', 'auto_scale', 'min_workers')
        }),
    )
    
    change_list_template = 'admin/queue_system/locationqueueconfig/change_list.html'
    
    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        
        # Get stats for the dashboard
        total_locations = LocationQueueConfig.objects.count()
        total_max_workers = LocationQueueConfig.objects.aggregate(total=models.Sum('max_workers'))['total'] or 0
        total_active_workers = LocationQueueConfig.objects.aggregate(total=models.Sum('active_workers'))['total'] or 0
        time_window_locations = LocationQueueConfig.objects.filter(has_time_window=True).count()
        
        extra_context.update({
            'total_locations': total_locations,
            'total_max_workers': total_max_workers,
            'total_active_workers': total_active_workers,
            'time_window_locations': time_window_locations,
        })
        
        return super().changelist_view(request, extra_context=extra_context)

@admin.register(QueuedJob)
class QueuedJobAdmin(admin.ModelAdmin):
    list_display = ('id', 'order_link', 'location', 'status_with_icon', 'retry_info',
                    'failure_reason', 'created_at', 'priority_flag', 'reviewed_by')
    list_filter = ('status', ReviewQueueFilter, FailureReasonFilter, RetryCountFilter,
                  'priority_flag', 'location', 'failure_reason', 'reviewed_by')
    search_fields = ('order__first_name', 'order__surname', 'order__customer_email',
                    'error_message', 'review_notes')
    readonly_fields = ('id', 'created_at', 'started_at', 'completed_at', 'worker_id',
                      'reviewed_at', 'error_details_formatted')
    inlines = [JobErrorInline]
    
    actions = ['mark_as_priority', 'requeue_failed_jobs', 'cancel_jobs', 'move_to_review',
              'requeue_from_review', 'create_job_for_order']

    change_list_template = 'admin/queue_system/queuedjob/change_list.html'

    def get_urls(self):
        from django.urls import path
        urls = super().get_urls()
        custom_urls = [
            path('queue-overview/', self.admin_site.admin_view(self.queue_overview_view), name='queue_overview'),
            path('review-queue/', self.admin_site.admin_view(self.review_queue_view), name='review_queue'),
        ]
        return custom_urls + urls

    def queue_overview_view(self, request):
        from django.shortcuts import redirect
        return redirect('queue_system:queue_overview')

    def review_queue_view(self, request):
        from django.shortcuts import redirect
        return redirect('queue_system:review_queue_dashboard')

    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'order', 'location', 'status', 'priority_flag')
        }),
        ('Timing', {
            'fields': ('created_at', 'scheduled_for', 'started_at', 'completed_at')
        }),
        ('Processing Details', {
            'fields': ('worker_id', 'retry_count', 'max_retries')
        }),
        ('Error Information', {
            'fields': ('failure_reason', 'error_message', 'error_details_formatted'),
            'classes': ('collapse',)
        }),
        ('Review Information', {
            'fields': ('reviewed_by', 'reviewed_at', 'review_notes'),
            'classes': ('collapse',)
        }),
        ('Requeue Information', {
            'fields': ('requeue_priority', 'requeue_reason'),
            'classes': ('collapse',)
        }),
    )
    
    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        
        # # WebSocket URL to context - use the correct admin live updates endpoint
        # Using global live updates instead of page-specific WebSocket
        # ws_scheme = 'wss' if request.is_secure() else 'ws'
        # extra_context['websocket_url'] = f"{ws_scheme}://{request.get_host()}/ws/admin/live/"

        # Get counts for the dashboard
        queued_count = QueuedJob.objects.filter(status='queued').count()
        processing_count = QueuedJob.objects.filter(status='processing').count()
        completed_count = QueuedJob.objects.filter(status='completed').count()
        failed_count = QueuedJob.objects.filter(status='failed').count()
        review_count = QueuedJob.objects.filter(status='review').count()
        requeued_count = QueuedJob.objects.filter(status='requeued').count()
        priority_count = QueuedJob.objects.filter(priority_flag=True).count()

        extra_context.update({
            'queued_count': queued_count,
            'processing_count': processing_count,
            'completed_count': completed_count,
            'failed_count': failed_count,
            'review_count': review_count,
            'requeued_count': requeued_count,
            'priority_count': priority_count,
        })
        
        return super().changelist_view(request, extra_context=extra_context)

    # Display methods
    def status_with_icon(self, obj):
        icons = {
            'queued': '⏳',
            'processing': '🔄',
            'completed': '✅',
            'failed': '❌',
            'review': '🔍',
            'requeued': '🔄',
            'cancelled': '🚫'
        }
        icon = icons.get(obj.status, '❓')
        return f"{icon} {obj.get_status_display()}"
    status_with_icon.short_description = 'Status'
    status_with_icon.admin_order_field = 'status'

    def retry_info(self, obj):
        max_retries = obj.max_retries if obj.max_retries else 3
        retry_display = f"{obj.retry_count}/{max_retries}"

        # Add visual indicator for max retries reached
        if obj.retry_count >= max_retries:
            return f"🚫 {retry_display} (Max reached)"
        elif obj.retry_count > 0:
            return f"🔄 {retry_display}"
        else:
            return f"🆕 {retry_display}"
    retry_info.short_description = 'Retries'
    retry_info.admin_order_field = 'retry_count'

    def error_details_formatted(self, obj):
        """Display detailed error information in a user-friendly format"""
        # First try to get the latest error with detailed description
        latest_error = obj.errors.order_by('-occurred_at').first()
        if latest_error and latest_error.error_message:
            # Show the detailed description
            formatted_message = latest_error.error_message.replace('\n', '<br>')
            error_display = f'<div style="font-family: monospace; white-space: pre-wrap; background: #f8f9fa; padding: 10px; border-radius: 4px; margin-bottom: 10px;">{formatted_message}</div>'

            # Also show JSON details if available
            if obj.error_details:
                import json
                formatted_json = json.dumps(obj.error_details, indent=2)
                error_display += f'<details><summary>Technical Details</summary><pre style="background: #f1f3f4; padding: 10px; border-radius: 4px; overflow-x: auto;">{formatted_json}</pre></details>'

            return error_display
        elif obj.error_details:
            import json
            formatted_json = json.dumps(obj.error_details, indent=2)
            return f'<pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">{formatted_json}</pre>'
        return "No error details"
    error_details_formatted.short_description = 'Error Details'
    error_details_formatted.allow_tags = True
    
    def requeue_failed_jobs(self, request, queryset):
        from .tasks import process_order
        count = 0
        for job in queryset.filter(status='failed'):
            job.status = 'queued'
            job.save()
            
            # Schedule for processing
            process_order.apply_async(
                args=[str(job.order.id)],
                queue=f'location.{job.location.id}'
            )
            count += 1
        
        self.message_user(request, f"{count} failed jobs have been requeued")
    requeue_failed_jobs.short_description = "Requeue selected failed jobs"
    
    def cancel_jobs(self, request, queryset):
        queryset.update(status='cancelled')
        self.message_user(request, f"{queryset.count()} jobs have been cancelled")
    cancel_jobs.short_description = "Cancel selected jobs"
    
    def mark_as_priority(self, request, queryset):
        queryset.update(priority_flag=True)
        self.message_user(request, f"{queryset.count()} jobs have been marked as priority")
    mark_as_priority.short_description = "Mark selected jobs as priority"

    def move_to_review(self, request, queryset):
        """Move failed jobs to review queue"""
        count = 0
        for job in queryset.filter(status='failed'):
            job.move_to_review(admin_user=request.user.username, notes="Moved via admin panel")
            count += 1

        self.message_user(request, f"{count} jobs moved to review queue")
    move_to_review.short_description = "Move failed jobs to review queue"

    def requeue_from_review(self, request, queryset):
        """Requeue jobs from review queue"""
        count = 0
        for job in queryset.filter(status='review'):
            job.requeue_job(
                admin_user=request.user.username,
                priority=False,
                reason="Requeued via admin panel"
            )
            count += 1

        self.message_user(request, f"{count} jobs requeued from review")
    requeue_from_review.short_description = "Requeue jobs from review queue"

    def create_job_for_order(self, request, queryset):
        """
        Create a job for an order directly from the admin interface.
        This is useful for debugging when signals aren't working.
        """
        from .tasks import schedule_job
        
        count = 0
        for job in queryset:
            try:
                # Create a new job for this order
                job_id = schedule_job(str(job.order.id))
                if job_id:
                    count += 1
            except Exception as e:
                self.message_user(request, f"Error creating job for order {job.order.id}: {str(e)}", level='ERROR')
        
        self.message_user(request, f"Created {count} new jobs")
    create_job_for_order.short_description = "Create new job for selected orders"

@admin.register(JobError)
class JobErrorAdmin(admin.ModelAdmin):
    list_display = ('job', 'occurred_at', 'error_message_short', 'failure_reason_display')
    list_filter = ('occurred_at',)
    search_fields = ('error_message', 'job__order__first_name', 'job__order__surname')
    readonly_fields = ('job', 'occurred_at', 'error_message_formatted', 'error_trace', 'error_details_formatted')
    
    change_list_template = 'admin/queue_system/joberror/change_list.html'
    
    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        
        # Get stats for the dashboard
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)
        
        total_errors = JobError.objects.count()
        errors_today = JobError.objects.filter(occurred_at__date=today).count()
        errors_this_week = JobError.objects.filter(occurred_at__date__gte=week_ago).count()
        
        # Get most common location with errors
        location_errors = JobError.objects.values('job__location__location_name') \
            .annotate(count=Count('id')) \
            .order_by('-count')
        
        most_common_location = location_errors[0]['job__location__location_name'] if location_errors else "N/A"
        
        extra_context.update({
            'total_errors': total_errors,
            'errors_today': errors_today,
            'errors_this_week': errors_this_week,
            'most_common_location': most_common_location,
        })
        
        return super().changelist_view(request, extra_context=extra_context)
    
    def error_message_short(self, obj):
        return obj.error_message[:100] + '...' if len(obj.error_message) > 100 else obj.error_message
    error_message_short.short_description = 'Error Message'

    def error_message_formatted(self, obj):
        """Display the detailed error description in a user-friendly format"""
        if obj.error_message:
            # Convert newlines to HTML breaks for better display
            formatted_message = obj.error_message.replace('\n', '<br>')
            return f'<div style="font-family: monospace; white-space: pre-wrap; background: #f8f9fa; padding: 10px; border-radius: 4px;">{formatted_message}</div>'
        return "No error message"
    error_message_formatted.short_description = 'Detailed Error Description'
    error_message_formatted.allow_tags = True

    def error_details_formatted(self, obj):
        """Display the error details JSON in a formatted way"""
        if obj.error_details:
            import json
            formatted_json = json.dumps(obj.error_details, indent=2)
            return f'<pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">{formatted_json}</pre>'
        return "No error details"
    error_details_formatted.short_description = 'Error Details (JSON)'
    error_details_formatted.allow_tags = True

    def failure_reason_display(self, obj):
        """Display the failure reason from error details"""
        if obj.error_details and 'failure_reason' in obj.error_details:
            reason = obj.error_details['failure_reason']
            # Make it more readable
            return reason.replace('_', ' ').title()
        return "Unknown"
    failure_reason_display.short_description = 'Failure Reason'

    def has_add_permission(self, request):
        return False







