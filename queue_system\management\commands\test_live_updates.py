from django.core.management.base import BaseCommand
from queue_system.signals import send_admin_live_update
import time

class Command(BaseCommand):
    help = 'Test live updates by sending periodic updates to admin WebSocket clients'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=5,
            help='Number of test updates to send (default: 5)',
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=3,
            help='Interval between updates in seconds (default: 3)',
        )

    def handle(self, *args, **options):
        count = options['count']
        interval = options['interval']
        
        self.stdout.write(
            self.style.SUCCESS(f"Sending {count} test updates with {interval}s intervals...")
        )
        
        for i in range(count):
            self.stdout.write(f"Sending update {i+1}/{count}...")
            send_admin_live_update()
            
            if i < count - 1:  # Don't sleep after the last update
                time.sleep(interval)
        
        self.stdout.write(
            self.style.SUCCESS("Test updates completed! Check admin interface for live updates.")
        )
