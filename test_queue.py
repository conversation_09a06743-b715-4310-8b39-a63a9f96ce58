#!/usr/bin/env python
"""
Test script to verify Celery Redis connection and queue functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from config.celery import app
from celery import current_app
import redis

def test_redis_connection():
    """Test direct Redis connection"""
    print("🔍 Testing Redis connection...")
    try:
        r = redis.Redis(host='localhost', port=6379, db=0)
        result = r.ping()
        print(f"✅ Redis ping successful: {result}")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

def test_celery_config():
    """Test Celery configuration"""
    print("\n🔍 Testing Celery configuration...")
    print(f"Broker URL: {app.conf.broker_url}")
    print(f"Result Backend: {app.conf.result_backend}")
    print(f"Broker Transport: {app.conf.broker_transport}")
    
def test_celery_connection():
    """Test Celery broker connection"""
    print("\n🔍 Testing Celery broker connection...")
    try:
        # Try to get broker connection
        with app.connection() as conn:
            conn.ensure_connection(max_retries=3)
        print("✅ Celery broker connection successful")
        return True
    except Exception as e:
        print(f"❌ Celery broker connection failed: {e}")
        return False

def test_simple_task():
    """Test sending a simple task"""
    print("\n🔍 Testing simple task...")
    try:
        # Import a simple task
        from queue_system.tasks import sync_worker_status_task
        
        # Send task to queue
        result = sync_worker_status_task.apply_async(queue='scheduler')
        print(f"✅ Task sent successfully: {result.id}")
        return True
    except Exception as e:
        print(f"❌ Task sending failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Celery Redis Queue Test")
    print("=" * 50)
    
    # Run tests
    redis_ok = test_redis_connection()
    test_celery_config()
    celery_ok = test_celery_connection()
    
    if redis_ok and celery_ok:
        task_ok = test_simple_task()
        if task_ok:
            print("\n🎉 All tests passed! Queue system should work.")
        else:
            print("\n⚠️ Task sending failed, but connections are OK.")
    else:
        print("\n❌ Connection tests failed. Check Redis and Celery configuration.")
