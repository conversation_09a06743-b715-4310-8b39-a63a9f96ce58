#!/bin/bash
# production_migration.sh - Consolidated migration script for queue_system app

set -e  # Exit on error

# Configuration
APP_NAME="queue_system"
MIGRATION_DIR="$APP_NAME/migrations"
DB_NAME="queue_system_locationqueueconfig"
COLUMNS_TO_CHECK=("auto_scale" "min_workers")

# Functions
check_migration_status() {
    echo "=== Current Migration Status ==="
    python manage.py showmigrations $APP_NAME
    echo ""
}

check_conflicts() {
    echo "=== Checking for Migration Conflicts ==="
    python -c "
from django.db.migrations.loader import MigrationLoader
from django.db import connections
loader = MigrationLoader(connections['default'])
conflicts = loader.detect_conflicts()
if '$APP_NAME' in conflicts:
    print('❌ Conflicts detected in $APP_NAME app:')
    for conflict in conflicts['$APP_NAME']:
        print(f'  - {conflict}')
    exit(1)
else:
    print('✅ No conflicts detected')
"
    echo ""
}

check_columns_exist() {
    echo "=== Checking Database Columns ==="
    python -c "
from django.db import connection
import sys

with connection.cursor() as cursor:
    try:
        cursor.execute(\"SELECT column_name FROM information_schema.columns WHERE table_name = '$DB_NAME'\")
        existing_columns = [row[0] for row in cursor.fetchall()]
        
        print('Existing columns in $DB_NAME:')
        for col in existing_columns:
            print(f'  - {col}')
        
        missing_columns = [col for col in $COLUMNS_TO_CHECK if col not in existing_columns]
        if missing_columns:
            print('Missing columns:')
            for col in missing_columns:
                print(f'  - {col}')
            sys.exit(1)
        else:
            print('✅ All required columns exist')
            sys.exit(0)
    except Exception as e:
        print(f'Error checking columns: {e}')
        sys.exit(1)
"
    return $?
}

clean_migration_files() {
    echo "=== Cleaning Migration Files ==="
    # Remove problematic migration files
    rm -f $MIGRATION_DIR/add_auto_scale_fields.py
    rm -f $MIGRATION_DIR/0003_add_auto_scale_fields.py
    echo "Removed conflicting migration files"
    echo ""
}

create_fake_migration() {
    echo "=== Creating Fake Migration ==="
    cat > $MIGRATION_DIR/0003_fake_auto_scale_fields.py << EOF
from django.db import migrations

class Migration(migrations.Migration):
    dependencies = [
        ('$APP_NAME', '0002_auto_check_existing_tables'),
    ]
    operations = []
EOF
    echo "Created fake migration file: 0003_fake_auto_scale_fields.py"
    echo ""
}

update_database_records() {
    echo "=== Updating Database Records ==="
    python -c "
from django.db import connection
with connection.cursor() as cursor:
    # Update or create migration record
    cursor.execute(\"\"\"
        INSERT INTO django_migrations (app, name, applied) 
        VALUES ('$APP_NAME', '0003_fake_auto_scale_fields', NOW())
        ON CONFLICT (app, name) DO NOTHING
    \"\"\")
    print('✅ Migration records updated')
"
    echo ""
}

add_missing_columns() {
    echo "=== Adding Missing Columns ==="
    python -c "
from django.db import connection
with connection.cursor() as cursor:
    try:
        if 'auto_scale' not in $COLUMNS_TO_CHECK:
            cursor.execute(\"ALTER TABLE $DB_NAME ADD COLUMN IF NOT EXISTS auto_scale BOOLEAN DEFAULT TRUE\")
            print('Added auto_scale column')
        
        if 'min_workers' not in $COLUMNS_TO_CHECK:
            cursor.execute(\"ALTER TABLE $DB_NAME ADD COLUMN IF NOT EXISTS min_workers INTEGER DEFAULT 0\")
            print('Added min_workers column')
    except Exception as e:
        print(f'Error adding columns: {e}')
        raise
"
    echo ""
}

squash_migrations() {
    echo "=== Squashing Migrations ==="
    python manage.py squashmigrations $APP_NAME 0003 --noinput
    echo "Migrations squashed up to 0003"
    echo ""
}

# Main Execution
echo "🚀 Starting Production Migration Process for $APP_NAME"
echo "============================================"

# 1. Initial checks
check_migration_status
check_conflicts

# 2. Check if columns exist
if check_columns_exist; then
    echo "✅ All required columns exist in database"
else
    echo "⚠️  Some columns are missing - attempting to add them"
    add_missing_columns
fi

# 3. Clean up migration files
clean_migration_files

# 4. Create fake migration
create_fake_migration

# 5. Update database records
update_database_records

# 6. Apply migrations
echo "=== Applying Migrations ==="
python manage.py migrate $APP_NAME
echo ""

# 7. Optional squash
read -p "Do you want to squash migrations? (y/n) " answer
if [ "$answer" = "y" ]; then
    squash_migrations
fi

# 8. Final status
echo "=== Final Migration Status ==="
python manage.py showmigrations $APP_NAME
python manage.py check_schema

echo "✨ Migration process completed successfully!"