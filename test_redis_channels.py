#!/usr/bin/env python
"""
Simple test for Redis channel layer
"""
import os
import sys
import django

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
    django.setup()

def test_redis_channels():
    """Test Redis channel layer"""
    setup_django()
    
    print("🔍 TESTING REDIS CHANNEL LAYER")
    print("=" * 50)
    
    try:
        from channels.layers import get_channel_layer
        from asgiref.sync import async_to_sync
        
        channel_layer = get_channel_layer()
        print(f"📊 Channel layer: {type(channel_layer).__name__}")
        
        if 'Redis' in type(channel_layer).__name__:
            print("✅ Using Redis channel layer")
        else:
            print("❌ Not using Redis channel layer")
            print(f"   Current: {type(channel_layer).__name__}")
            return False
            
        # Test sending a message
        print("🧪 Testing message send...")
        
        test_data = {
            "type": "admin_update",
            "data": {
                "type": "test",
                "message": "Redis test successful"
            }
        }
        
        async_to_sync(channel_layer.group_send)(
            "admin_updates",
            test_data
        )
        
        print("✅ Message sent successfully")
        return True
        
    except Exception as e:
        print(f"❌ Redis channel test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🎯 REDIS CHANNEL LAYER TEST")
    print("=" * 60)
    
    success = test_redis_channels()
    
    if success:
        print("\n🎉 Redis channel layer is working!")
        print("📋 Next steps:")
        print("1. Restart Django server")
        print("2. Open queue admin page")
        print("3. Test live updates")
    else:
        print("\n❌ Redis channel layer failed")
        print("🔧 Check Redis connection and configuration")

if __name__ == "__main__":
    main()
