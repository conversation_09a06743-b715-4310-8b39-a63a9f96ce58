#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update an existing job with detailed error information.
This demonstrates the new detailed error descriptions.
"""

import os
import sys
import django

# Setup Django environment
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.tasks import parse_bot_error
from queue_system.models import QueuedJob, JobError
from django.utils import timezone

def update_job_with_detailed_error(job_id):
    """Update an existing job with detailed error information"""
    
    try:
        # Get the job
        job = QueuedJob.objects.get(id=job_id)
        print(f"Found job #{job.id} for customer: {job.order.first_name} {job.order.surname}")
        print(f"Current status: {job.status}")
        print(f"Current failure reason: {job.failure_reason}")
        
        # Simulate a gender dropdown error (the most common one)
        error_message = "Error clicking element: Unable to select gender from the drop down menu"
        error_trace = """Traceback (most recent call last):
  File 'barbados_form_1.py', line 45, in fill_form
    select_gender(driver, 'Male')
  File 'barbados_form_1.py', line 78, in select_gender
    raise Exception('Unable to select gender from the drop down menu')
Exception: Unable to select gender from the drop down menu"""
        
        # Parse the error to get detailed description
        error_msg, detailed_description, failure_reason = parse_bot_error(error_message, error_trace)
        
        print(f"\nParsed error information:")
        print(f"Error Message: {error_msg}")
        print(f"Failure Reason: {failure_reason}")
        print(f"Detailed Description:")
        print(detailed_description)
        
        # Create detailed error record
        error_details = {
            'error_type': 'Exception',
            'error_message': error_message,
            'detailed_description': detailed_description,
            'order_id': str(job.order.id),
            'location': job.location.location_name,
            'timestamp': timezone.now().isoformat(),
            'bot_module': 'Barbados_form_1',
            'context': 'Bot execution failed during form processing'
        }
        
        # Create the JobError record with detailed description
        job_error = JobError.objects.create(
            job=job,
            error_message=detailed_description,  # This is the detailed description
            error_trace=error_trace,
            error_details=error_details
        )
        
        # Update job with detailed failure reason
        job.failure_reason = failure_reason
        job.error_message = detailed_description  # Update job's error message too
        job.save()
        
        print(f"\n✅ Successfully updated job #{job.id}")
        print(f"📋 New failure reason: {failure_reason}")
        print(f"🔗 View job details: http://localhost:8000/queue/admin/job/{job.id}/")
        
        return job
        
    except QueuedJob.DoesNotExist:
        print(f"❌ Job #{job_id} not found")
        return None
    except Exception as e:
        print(f"❌ Error updating job: {str(e)}")
        return None

def list_available_jobs():
    """List available jobs that can be updated"""
    
    print("📋 Available jobs:")
    print("=" * 50)
    
    jobs = QueuedJob.objects.filter(status__in=['failed', 'review']).order_by('-created_at')[:10]
    
    if not jobs:
        print("No failed or review jobs found.")
        return
    
    for job in jobs:
        print(f"Job #{job.id}: {job.order.first_name} {job.order.surname}")
        print(f"  Status: {job.status}")
        print(f"  Location: {job.location.location_name}")
        print(f"  Current failure reason: {job.failure_reason or 'Unknown'}")
        print(f"  Created: {job.created_at}")
        print()

if __name__ == "__main__":
    print("🔧 Update Existing Job with Detailed Error")
    print("=" * 50)
    
    # List available jobs
    list_available_jobs()
    
    # Get job ID from user or use a default
    if len(sys.argv) > 1:
        job_id = sys.argv[1]
    else:
        # Try to find a job to update
        job = QueuedJob.objects.filter(status__in=['failed', 'review']).first()
        if job:
            job_id = job.id
            print(f"Using job #{job_id} for demonstration")
        else:
            print("❌ No failed or review jobs found to update")
            print("💡 Create a job first or specify a job ID as argument")
            sys.exit(1)
    
    # Update the job
    updated_job = update_job_with_detailed_error(job_id)
    
    if updated_job:
        print(f"\n🎉 Job updated successfully!")
        print(f"🔗 View in admin: http://localhost:8000/admin/queue_system/queuedjob/{updated_job.id}/change/")
        print(f"🔗 View job details: http://localhost:8000/queue/admin/job/{updated_job.id}/")
